import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/core/widgets/title_main.dart';
import 'package:edus_student_ms/features/fees/presentation/components/tabBar/tab_bar_fees.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Fees extends StatefulWidget {
  const Fees({super.key});

  @override
  State<Fees> createState() => _FeesState();
}

class _FeesState extends State<Fees> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TitleMain(title: AppString.fees),
      ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: const TabBarFees(),
          ),
        ),
      ),
    );
  }
}
