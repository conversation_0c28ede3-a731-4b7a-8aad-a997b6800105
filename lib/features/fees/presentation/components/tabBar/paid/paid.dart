import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class TabBarPaid extends StatelessWidget {
  const TabBarPaid({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: ListView.builder(
          itemCount: 2,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: EdgeInsets.only(bottom: 8.h),
              child: Container(
                height: 67,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                  color:AppColor.kBgColor,
                  border: Border.all(
                    color:AppColor.kfessbordercolor,
                    width: 1,
                  ),
                ),
                padding: EdgeInsets.symmetric(horizontal: 8.h, vertical: 8.h),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Waleed Soliman’s Math Class',
                          textAlign: TextAlign.left,
                          style: AppFont.kFontPoppinsStyle.copyWith(
                              fontSize: 14.sp, fontWeight: FontWeight.w600),
                        ),
                        Text(
                          '06 June',
                          textAlign: TextAlign.left,
                          style: AppFont.kFontPoppinsStyle.copyWith(
                              fontSize: 11.sp, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    SizedBox(height: 6.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '150 EGP',
                              textAlign: TextAlign.left,
                              style: AppFont.kFontPoppinsStyle.copyWith(
                                  fontSize: 14.sp, fontWeight: FontWeight.w600),
                            ),
                            SizedBox(width: 16.w),
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.r),
                                color:AppColor.kfeesPaid,
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 4.h, vertical: 4.h),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Paid',
                                    textAlign: TextAlign.left,
                                    style: AppFont.kFontPoppinsStyle.copyWith(
                                        color: AppColor.kBgColor,
                                        fontSize: 10.sp,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        GestureDetector(
                            onTap: () {},
                            child: SvgPicture.asset(AppIcon.karrowDown))
                      ],
                    ),
                  ],
                ),
              ),
            );
          }),
    );
  }
}
