import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/attendancerecords/attendance_racords.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/qrcode/qr_code.dart';
import 'package:edus_student_ms/features/fees/presentation/components/tabBar/paid/paid.dart';
import 'package:edus_student_ms/features/fees/presentation/components/tabBar/pending/pending.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TabBarFees extends StatefulWidget {
  const TabBarFees({super.key});

  @override
  State<TabBarFees> createState() => _TabBarFeesState();
}

class _TabBarFeesState extends State<TabBarFees>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    _tabController = TabController(length: 2, initialIndex: 0, vsync: this);
    _tabController.addListener(_handleTabSelection);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 46.h,
          decoration: BoxDecoration(
            color: AppColor.kBgColor,
            borderRadius: BorderRadius.circular(5.r),
            boxShadow: const [], // إزالة الظل
          ),
          child: Row(
            children: [
              Expanded(
                flex: _currentIndex == 0 ? 5 : 2,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _tabController.index = 0;
                      _currentIndex = 0;
                    });
                  },
                  child: AnimatedContainer(
                    duration:
                        const Duration(milliseconds: 0), // تقليل المدة الزمنية
                    curve: Curves.easeInOut, // إضافة منحنى تسريع ملائم
                    decoration: BoxDecoration(
                      color: _currentIndex == 0
                          ? AppColor.kPrimaryColor
                          : AppColor.kBgColor,
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      AppString.pending,
                      style: _currentIndex == 0
                          ? AppFont.kFontPoppinsStyle.copyWith(
                              fontSize: 17.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            )
                          : AppFont.kFontPoppinsStyle.copyWith(
                              fontSize: 17.sp,
                              fontWeight: FontWeight.w500,
                              color: AppColor.kGrayColor,
                            ),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: _currentIndex == 1 ? 3 : 2,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _tabController.index = 1;
                      _currentIndex = 1;
                    });
                  },
                  child: AnimatedContainer(
                    duration:
                        const Duration(milliseconds: 0), // تقليل المدة الزمنية
                    curve: Curves.easeInOut, // إضافة منحنى تسريع ملائم
                    decoration: BoxDecoration(
                      color: _currentIndex == 1
                          ? AppColor.kPrimaryColor
                          : AppColor.kBgColor,
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      AppString.paid,
                      style: _currentIndex == 1
                          ? AppFont.kFontPoppinsStyle.copyWith(
                              fontSize: 17.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            )
                          : AppFont.kFontPoppinsStyle.copyWith(
                              fontSize: 17.sp,
                              fontWeight: FontWeight.w500,
                              color: AppColor.kGrayColor,
                            ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 600.h, // حدد الارتفاع المناسب هنا
          child: TabBarView(
            controller: _tabController,
            children: const [TabBarPending(), TabBarPaid()],
          ),
        )
      ],
    );
  }

  _handleTabSelection() {
    setState(() {
      _currentIndex = _tabController.index;
    });
  }
}
