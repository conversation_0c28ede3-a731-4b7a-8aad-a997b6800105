import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';

import '../../../../core/usecase/base_usecase.dart';
import '../entities/login/login_data.dart';
import '../entities/login/login_error.dart';
import '../repository/auth_repository.dart';

class DoLoginUseCase implements UseCase<LoginData, LoginParams> {
  final AuthRepository repo;

  DoLoginUseCase(this.repo);

  @override
  Future<Either<Failure<LoginError>, LoginData>> call(
      LoginParams params) async {
    return await repo.doLogin(params);
  }
}

class LoginParams {
  final String email;
  final String password;
  final String language;

  LoginParams(
      {required this.email, required this.password, required this.language});

  Map<String, dynamic> toJson() => {
        'login_input': email,
        'password': password,
        'language': language,
      };
}
