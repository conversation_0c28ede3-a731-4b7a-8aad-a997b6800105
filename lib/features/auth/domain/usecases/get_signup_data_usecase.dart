import 'package:dartz/dartz.dart';
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_error.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecase/base_usecase.dart';
import '../repository/auth_repository.dart';

class DoSignUpUseCase implements UseCase<SignUpData, SignUpParams> {
  final AuthRepository repo;

  DoSignUpUseCase(this.repo);

  @override
  Future<Either<Failure<SignUpError>, SignUpData>> call(
      SignUpParams params) async {
    return await repo.doSignUp(params);
  }
}

class SignUpParams {
  final String name;
  final String email;
  final String phone;
  final String password;
  final String language;
  final String grade;

  SignUpParams(
      {required this.name,
      required this.email,
      required this.phone,
      required this.password,
      required this.language,
      required this.grade});

  Map<String, dynamic> toJson() => {
        'student_name': name,
        'email': email,
        'contact_number': phone,
        'password': password,
        'language': language,
        'grade': grade,
      };
}
