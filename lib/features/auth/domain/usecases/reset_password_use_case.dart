import 'package:dartz/dartz.dart';


import '../../../../core/error/failures.dart';
import '../../../../core/usecase/base_usecase.dart';
import '../repository/auth_repository.dart';

class ResetPasswordUseCase extends UseCase<String, String> {
  final AuthRepository repo;

  ResetPasswordUseCase(this.repo);

  @override
  Future<Either<Failure<String>, String>> call(String email) async {
    return await repo.resetPassword(email);
  }
}
