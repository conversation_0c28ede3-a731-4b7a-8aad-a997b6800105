import 'package:dartz/dartz.dart';
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_error.dart';
import 'package:edus_student_ms/features/auth/domain/usecases/get_signup_data_usecase.dart';

import '../../../../core/error/failures.dart';
import '../entities/login/login_data.dart';
import '../entities/login/login_error.dart';
import '../usecases/get_login_data_usecase.dart';

abstract class AuthRepository {
  Future<Either<Failure<LoginError>, LoginData>> doLogin(LoginParams params);
  Future<Either<Failure<SignUpError>, SignUpData>> doSignUp(
      SignUpParams params);
  Future<Either<Failure<String>, String>> resetPassword(String email);
}
