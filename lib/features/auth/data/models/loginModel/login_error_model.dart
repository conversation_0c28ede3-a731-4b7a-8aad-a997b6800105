// {email: [This field may not be blank.], password: [This field may not be blank.]}

import '../../../domain/entities/login/login_error.dart';

// non_field_errors: [Incorrect email or password]
class LoginErrorModel extends LoginError {
  const LoginErrorModel(
      {String? message, })
      : super(message: message,);

  factory LoginErrorModel.fromJson(Map<String, dynamic> json) {
    return LoginErrorModel(
      message: json['message'] != null ? json['message'][0] : null,

    );
  }
}
