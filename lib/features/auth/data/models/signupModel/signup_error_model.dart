// {email: [This field may not be blank.], password: [This field may not be blank.]}


// non_field_errors: [Incorrect email or password]
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_error.dart';

class SignUpErrorModel extends SignUpError {
  const SignUpErrorModel(
      {String? message, })
      : super(message: message,);

  factory SignUpErrorModel.fromJson(Map<String, dynamic> json) {
    return SignUpErrorModel(
      message: json['message'] != null ? json['message'][0] : null,

    );
  }
}
