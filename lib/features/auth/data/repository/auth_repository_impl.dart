import 'package:dartz/dartz.dart';
import 'package:edus_student_ms/features/auth/data/models/signupModel/signup_error_model.dart';
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_error.dart';
import 'package:edus_student_ms/features/auth/domain/usecases/get_signup_data_usecase.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/services/service_locator.dart';
import '../../../../core/services/token_service/token_storage.dart';
import '../../domain/entities/login/login_data.dart';
import '../../domain/entities/login/login_error.dart';
import '../../domain/repository/auth_repository.dart';
import '../../domain/usecases/get_login_data_usecase.dart';
import '../datasource/auth_remote_datasource.dart';
import '../models/loginModel/login_error_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;

  AuthRepositoryImpl(this.remoteDataSource);

  @override
  Future<Either<Failure<LoginError>, LoginData>> doLogin(LoginParams params) async {
    try {
      final result = await remoteDataSource.doLogin(params);
      sl<TokenStorage>().storeToken(result.userData?.token??"");
      sl<TokenStorage>().storeUserId(result.userData?.userId??0);
      print(result.userData?.token);
      return Right(result);
    } on ServerException<LoginErrorModel> catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure<String>, String>> resetPassword(String email) async {
    try {
      final result = await remoteDataSource.resetPassword(email);
      return Right(result);
    } on ServerException<String> catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
@override
  Future<Either<Failure<SignUpError>, SignUpData>> doSignUp(SignUpParams params) async {
    try {
      final result = await remoteDataSource.doSignUp(params);
      print(result);
      return Right(result);
    } on ServerException<SignUpErrorModel> catch (e) {
      return Left(ServerFailure(e.message ));
    }
  }
}
