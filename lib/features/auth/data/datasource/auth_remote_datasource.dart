import 'dart:convert';
import 'dart:developer';

import 'package:edus_student_ms/features/auth/data/models/signupModel/signup_data_model.dart';
import 'package:edus_student_ms/features/auth/domain/usecases/get_signup_data_usecase.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/network/api_constant.dart';
import '../../domain/usecases/get_login_data_usecase.dart';
import '../models/loginModel/login_data_model.dart';
import 'package:dio/dio.dart';

final Dio dio = Dio(BaseOptions(
  baseUrl: ApiConstants.baseUrl,
  connectTimeout: const Duration(seconds: 3000),
  receiveTimeout: const Duration(seconds: 3000),
  sendTimeout: const Duration(seconds: 3000),
  validateStatus: (status) {
    return status! < 501;
  },
  headers: {
    'Content-Type': 'application/json',
  },
));

abstract class AuthRemoteDataSource {
  Future<LoginModel> doLogin(LoginParams params);
  Future<SignUpModel> doSignUp(SignUpParams params);

  Future<String> resetPassword(String email);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  AuthRemoteDataSourceImpl();

  @override
  Future<LoginModel> doLogin(LoginParams params) async {
    final response = await dio.post(ApiConstants.loginUrl,
        data: FormData.fromMap(params.toJson()),
        options: Options(headers: {"Content-Type": "multipart/form-data"}));
    log("${response.data}-----password-${params.password}  -email-${params.email} ");

    return switch (response.statusCode!) {
      >= 200 && < 300 =>
        LoginModel.fromJson(const JsonDecoder().convert(response.data)),
      404 => throw ServerException<String>("Email not found"),
      >= 400 && < 500 => throw ServerException<String>(response.data),
      _ => throw ServerException("${response.data}")
    };
  }

  @override
  Future<SignUpModel> doSignUp(SignUpParams params) async {
    final response = await dio.post(ApiConstants.signUpUrl,
        data: FormData.fromMap(params.toJson()),
        options: Options(headers: {"Content-Type": "multipart/form-data"}));
    log("${response.data}-----password-${params.password}  -email-${params.email}  -name-${params.name}  -phone-${params.phone}");

    return switch (response.statusCode!) {
      >= 200 && < 300 =>
        SignUpModel.fromJson(const JsonDecoder().convert(response.data)),
      404 => throw ServerException<String>("Email not found"),
      >= 400 && < 500 => throw ServerException<String>(response.data),
      _ => throw ServerException("${response.data}")
    };
  }

  @override
  Future<String> resetPassword(String email) async {
    final response = await dio.get(ApiConstants.resetPasswordUrl);

    return response.data;
  }
}
