import 'dart:async';
import 'dart:developer';

import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/auth/domain/usecases/get_signup_data_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/utils/enums.dart';
import '../../domain/entities/login/login_data.dart';
import '../../domain/usecases/get_login_data_usecase.dart';
import '../../domain/usecases/reset_password_use_case.dart';

part 'auth_event.dart';

part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final DoLoginUseCase _doLoginUseCase;
  final DoSignUpUseCase _doSignUpUseCase;
  final ResetPasswordUseCase _resetPasswordUseCase;

  AuthBloc(
      this._doLoginUseCase, this._resetPasswordUseCase, this._doSignUpUseCase)
      : super(const AuthState()) {
    on<DoLoginEvent>(_doLoginEvent);
    on<DoSignUpEvent>(_doSignUpEvent);
    on<ResetPasswordEvent>(_resetPasswordEvent);
  }

  FutureOr<void> _doLoginEvent(
      DoLoginEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final res = await _doLoginUseCase(event.params);
    res.fold(
        (l) => emit(state.copyWith(
            loginData: null,
            status: RequestStatus.error,
            error: l.error.message)), (r) {
      if (r.status == 'fail') {
        emit(state.copyWith(
            loginData: null, status: RequestStatus.error, error: r.message));
      } else {
        emit(state.copyWith(
            status: RequestStatus.success, error: null, loginData: r));
      }
    });
  }

  FutureOr<void> _doSignUpEvent(
      DoSignUpEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final res = await _doSignUpUseCase(event.params);
    res.fold(
        (l) => emit(state.copyWith(
            signUpData: null,
            status: RequestStatus.error,
            error: l.error.message)), (r) {
      if (r.status == 'fail') {
        emit(state.copyWith(
          status: RequestStatus.error,
          error: r.message,
        ));
        log("-r=error======>${state.error}");
      } else {
        emit(state.copyWith(
            status: RequestStatus.success, error: null, signUpData: r));
      }
    });
  }

  FutureOr<void> _resetPasswordEvent(
      ResetPasswordEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final res = await _resetPasswordUseCase(event.email);
    res.fold(
        (l) =>
            emit(state.copyWith(status: RequestStatus.error, error: l.error)),
        (r) {
      emit(state.copyWith(status: RequestStatus.success, error: null));
    });
  }
}
