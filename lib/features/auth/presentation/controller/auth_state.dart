part of 'auth_bloc.dart';

class AuthState extends Equatable {
  final RequestStatus status;
  final String error;
  final LoginData? loginData;
  final SignUpData? signUpData;

  const AuthState(
      {
      this.loginData,
      this.signUpData,
      this.status = RequestStatus.initial,
      this.error = ""});

  AuthState copyWith(
      {
      RequestStatus? status,
      String? error,
      LoginData? loginData,
      SignUpData? signUpData,
      }) {
    return AuthState(
        loginData: loginData ?? loginData,
        signUpData: signUpData ?? signUpData,
        status: status ?? this.status,
        error: error ?? this.error);
  }

  @override
  List<Object> get props => [status];
}
