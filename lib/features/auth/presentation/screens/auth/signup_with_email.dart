import 'package:edus_student_ms/core/services/service_locator.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/enums.dart';
import 'package:edus_student_ms/features/auth/domain/usecases/get_signup_data_usecase.dart';
import 'package:edus_student_ms/features/auth/presentation/controller/auth_bloc.dart';
import 'package:edus_student_ms/features/auth/presentation/screens/auth/login.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:form_validator/form_validator.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../../core/config/app_routes.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/utils/app_font.dart';
import '../../../../../core/utils/app_string.dart';
import '../../../../../core/widgets/buttons/sign_up_button.dart';
import '../../../../../core/widgets/input/password_input.dart';
import '../../../../../core/widgets/input/text_input.dart';

class SignupWithEmail extends StatefulWidget {
  const SignupWithEmail({super.key});

  @override
  State<SignupWithEmail> createState() => _SignupWithEmailState();
}

class _SignupWithEmailState extends State<SignupWithEmail> {
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController mobileNumberController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmedPasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
            backgroundColor: AppColor.kBgColor,
            body: Form(
              key: _formKey,
              child: SafeArea(
                  child: SingleChildScrollView(
                      child: Column(children: [
                SizedBox(height: 28.h),
                Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  SizedBox(width: 16.w),
                  SizedBox(
                    width: 24.w,
                    height: 24.w,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: const Icon(Icons.arrow_back_ios_new,
                            color: Color(0xff94A5AB))),
                  ),
                  SizedBox(width: 87.w),
                  SvgPicture.asset(AppIcon.kLogo),
                ]),
                SizedBox(height: 40.h),
                TextInput(
                    icon: AppIcon.kUserIcon,
                    hint: AppString.fullName,
                    inputType: TextInputType.name,
                    controller: nameController,
                    validator: ValidationBuilder().minLength(4).build()),
                SizedBox(height: 16.h),
                TextInput(
                    icon: AppIcon.kMailIcon,
                    hint: AppString.emailAddress,
                    inputType: TextInputType.emailAddress,
                    controller: emailController,
                    validator: ValidationBuilder().email().build()),
                SizedBox(height: 16.h),
                TextInput(
                    icon: AppIcon.kPhoneIcon,
                    hint: AppString.mobileNumber,
                    inputType: TextInputType.phone,
                    controller: mobileNumberController,
                    validator: ValidationBuilder().phone().build()),
                SizedBox(height: 16.h),
                PasswordInput(
                    icon: AppIcon.kLockIcon,
                    hint: AppString.password,
                    inputType: TextInputType.visiblePassword,
                    controller: passwordController,
                    validator: ValidationBuilder().minLength(8).build()),
                SizedBox(height: 16.h),
                PasswordInput(
                    icon: AppIcon.kLockIcon,
                    hint: AppString.confirmedPassword,
                    inputType: TextInputType.visiblePassword,
                    controller: confirmedPasswordController,
                    validator: (value) {
                      return value == passwordController.text
                          ? null
                          : "Password not match";
                    }),
                SizedBox(height: 24.h),
                BlocConsumer<AuthBloc, AuthState>(
                  listener: (context, state) {
                    if (ModalRoute.of(context)!.isCurrent) {
                      if (state.status == RequestStatus.success) {
                        print("Current Status: ${state.status}");
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => Login(),
                            ));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text("Success"),
                            duration: Duration(seconds: 2),
                          ),
                        );
                        // ScaffoldMessenger.of(context)
                        //     .showSnackBar(
                        //   SnackBar(
                        //     content: Text(state
                        //         .signUpData!.message),
                        //     duration:
                        //         Duration(seconds: 2),
                        //   ),
                        // );
                      } else if (state.status == RequestStatus.error) {
                        print("Current Status: ${state.status}");
                        print("Current Error: ${state.error}");
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(state.error),
                            duration: Duration(seconds: 2),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  builder: (context, state) {
                    return state.status == RequestStatus.loading
                        ? SizedBox(
                            height: 48.h,
                            child: Center(
                              child: const CircularProgressIndicator(
                                color: AppColor.kPrimaryColor,
                              ),
                            ),
                          )
                        : SignUpButton(
                            onPress: () {
                              if (!_formKey.currentState!.validate()) {
                               
                              } else {
                                sl<AuthBloc>().add(DoSignUpEvent(SignUpParams(
                                    name: nameController.text,
                                    email: emailController.text,
                                    phone: mobileNumberController.text,
                                    password: passwordController.text,
                                    language: "en",
                                    grade: "1")));
                              }
                            },
                          );
                  },
                ),
                Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                    child: RichText(
                        text: TextSpan(children: [
                      TextSpan(
                          text: AppString.byClickingOnSignup,
                          style: GoogleFonts.poppins(
                              color: Colors.black,
                              fontWeight: FontWeight.w400,
                              fontSize: 11.sp)),
                      TextSpan(
                          text: AppString.termsConditions,
                          style: GoogleFonts.poppins(
                              color: AppColor.kPrimaryColor,
                              fontWeight: FontWeight.w500,
                              fontSize: 11.sp)),
                      TextSpan(
                          text: AppString.and,
                          style: GoogleFonts.poppins(
                              color: Colors.black,
                              fontWeight: FontWeight.w400,
                              fontSize: 11.sp)),
                      TextSpan(
                          onEnter: (dd) {
                            Navigator.pushNamed(context, kLogin);
                          },
                          text: AppString.privacyPolicy,
                          style: GoogleFonts.poppins(
                              color: AppColor.kPrimaryColor,
                              fontWeight: FontWeight.w500,
                              fontSize: 11.sp))
                    ]))),
                SizedBox(height: 145.h),
                Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  const PoppinsText(
                      text: AppString.alreadyHaveAnAccount,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.black),
                  InkWell(
                      onTap: () {
                        Navigator.pushReplacementNamed(context, kLogin);
                      },
                      child: const PoppinsText(
                          text: AppString.bLogIn,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColor.kPrimaryColor))
                ]),
                SizedBox(height: 20.h)
              ]))),
            )));
  }
}
