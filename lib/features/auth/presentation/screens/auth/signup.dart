import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../../core/config/app_routes.dart';
import '../../../../../core/utils/app_font.dart';
import '../../../../../core/utils/app_icon.dart';

class Signup extends StatefulWidget {
  const Signup({super.key});

  @override
  State<Signup> createState() => _SignupState();
}

class _SignupState extends State<Signup> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
            backgroundColor: AppColor.kBgColor,
            body: SafeArea(
                child: SingleChildScrollView(
                    child: Column(children: [
              SizedBox(height: 28.h),
              Center(
                child: SvgPicture.asset(AppIcon.kLogo),
              ),
              SizedBox(height: 79.h),
              Container(
                height: 48.h,
                width: 343.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: AppColor.kBgColor,
                    border: Border.all(color: const Color(0xffD9D9D9))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      AppIcon.kGoogleIcon,
                      height: 24.h,
                      width: 24.h,
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    const PoppinsText(
                      text: AppString.signUpWithGoogle,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 8.h,
              ),
              Container(
                height: 48.h,
                width: 343.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: AppColor.kBgColor,
                    border: Border.all(color: const Color(0xffD9D9D9))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      AppIcon.kFacebookIcon,
                      height: 24.h,
                      width: 24.h,
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    const PoppinsText(
                      text: AppString.signUpWithFacebook,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 8.h,
              ),
              Container(
                height: 48.h,
                width: 343.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: AppColor.kBgColor,
                    border: Border.all(color: const Color(0xffD9D9D9))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      AppIcon.kAppleIcon,
                      height: 24.h,
                      width: 24.h,
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    const PoppinsText(
                      text: AppString.signUpWithApple,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 8.h,
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, kSignupWithEmail);
                },
                child: Container(
                  height: 48.h,
                  width: 343.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: AppColor.kPrimaryColor,
                      border: Border.all(color: AppColor.kPrimaryColor)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppIcon.kMailIcon,
                        color: Colors.white,
                        height: 24.h,
                        width: 24.h,
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      const PoppinsText(
                        text: AppString.signUpWithEmail,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 6.h,
              ),
              Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  child: RichText(
                      text: TextSpan(children: [
                    TextSpan(
                        text: AppString.byClickingOnSignup,
                        style: GoogleFonts.poppins(
                            color: Colors.black,
                            fontWeight: FontWeight.w400,
                            fontSize: 11.sp)),
                    TextSpan(
                        text: AppString.termsConditions,
                        style: GoogleFonts.poppins(
                            color: AppColor.kPrimaryColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 11.sp)),
                    TextSpan(
                        text: AppString.and,
                        style: GoogleFonts.poppins(
                            color: Colors.black,
                            fontWeight: FontWeight.w400,
                            fontSize: 11.sp)),
                    TextSpan(
                        onEnter: (dd) {
                          Navigator.pushNamed(context, kLogin);
                        },
                        text: AppString.privacyPolicy,
                        style: GoogleFonts.poppins(
                            color: AppColor.kPrimaryColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 11.sp))
                  ]))),
              SizedBox(height: 266.h),
              Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                const PoppinsText(
                    text: AppString.alreadyHaveAnAccount,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.black),
                InkWell(
                    onTap: () {
                      Navigator.pushReplacementNamed(context, kLogin);
                    },
                    child: const PoppinsText(
                        text: AppString.bLogIn,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColor.kPrimaryColor))
              ]),
              SizedBox(height: 20.h)
            ])))));
  }
}
