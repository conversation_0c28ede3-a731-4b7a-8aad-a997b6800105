import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class CalendarWidget extends StatefulWidget {
  const CalendarWidget({super.key});

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  late DateTime _selectedDate;
  late DateTime _currentDate;
  late PageController _pageController;
  late ScrollController _scrollController;
  bool isCurrentDateSelected = true;

  List<DateTime> _getDaysInMonth(DateTime month) {
    final lastDay = DateTime(month.year, month.month + 1, 0);
    return List.generate(
      lastDay.day,
      (index) => DateTime(month.year, month.month, index + 1),
    );
  }

  @override
  void initState() {
    super.initState();
    _currentDate = DateTime.now();
    _selectedDate = DateTime.now(); // Set initial selected date to current date
    _pageController = PageController(
        initialPage: (_currentDate.year - DateTime.now().year) * 12 +
            _currentDate.month -
            1);
    _scrollController =
        ScrollController(initialScrollOffset: (_selectedDate.day - 1) * 76.w);
    print(
        'Selected Date: $_selectedDate'); // Print selected date for verification
  }

  @override
  void dispose() {
    _pageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _handleDateSelection(DateTime selectedDay) {
    setState(() {
      _selectedDate = selectedDay; // Update selected date
      isCurrentDateSelected = _selectedDate.day == DateTime.now().day &&
          _selectedDate.month == DateTime.now().month;
    });
    print(
        'Selected Date: $_selectedDate'); // Print selected date for verification
  }

  @override
  Widget build(BuildContext context) {
    // Get days of the current month
    return SizedBox(
      height: 86.h,
      child: PageView.builder(
        controller: _pageController,
        itemCount: 120, // 10 years * 12 months
        onPageChanged: (index) {
          setState(() {
            _currentDate = DateTime(
              DateTime.now().year + (index ~/ 12),
              (index % 12) + 1,
            );
            _scrollController.animateTo((_currentDate.day - 1) * 76.w,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut);
          });
        },
        itemBuilder: (context, index) {
          final currentMonth = DateTime(
            DateTime.now().year + (index ~/ 12),
            (index % 12) + 1,
          );
          final days = _getDaysInMonth(currentMonth);
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 16.w),
                child: PoppinsText(
                  text: DateFormat.yMMMM().format(currentMonth),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  scrollDirection: Axis.horizontal,
                  itemCount: days.length,
                  itemBuilder: (context, dayIndex) {
                    final day = days[dayIndex];
                    final isSelected = day == _selectedDate;
                    final isCurrentDay = day.day == DateTime.now().day &&
                        currentMonth.month == DateTime.now().month;
                    return GestureDetector(
                      onTap: () => _handleDateSelection(day),
                      child: Container(
                        width: 74.w,
                        height: 64.w,
                        margin: EdgeInsets.symmetric(horizontal: 2.w),
                        decoration: BoxDecoration(
                          color: isSelected ||
                                  (isCurrentDateSelected && isCurrentDay)
                              ? AppColor.kPrimaryColor
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(5.r),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            PoppinsText(
                              text: '${day.day}',
                              fontSize: 13,
                              fontWeight: isSelected ||
                                      isCurrentDateSelected && isCurrentDay
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isSelected ||
                                      isCurrentDateSelected && isCurrentDay
                                  ? AppColor.kBgColor
                                  : Colors.black,
                            ),
                            SizedBox(height: 4.h),
                            PoppinsText(
                              text: DateFormat.EEEE().format(day),
                              fontSize: 11,
                              fontWeight: isSelected ||
                                      isCurrentDateSelected && isCurrentDay
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isSelected ||
                                      isCurrentDateSelected && isCurrentDay
                                  ? AppColor.kBgColor
                                  : Colors.black,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
