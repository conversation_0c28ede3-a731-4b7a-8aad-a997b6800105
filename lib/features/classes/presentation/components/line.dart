import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Line extends StatelessWidget {
  const Line({super.key});

  @override
  Widget build(BuildContext context) {
    return  Positioned(
                top: 40.w + 15.w / 2, // نصف ارتفاع الدائرة الأولى
                left: 68.5.w,
                child: Container(
                  width: 3.w,
                  height: 532.h -
                      (35.w +
                          15.w /
                              2), // ارتفاع القائمة ناقص نصف ارتفاع الدائرة الأولى
                  color: AppColor.kclassesLineListColor,
                ),
              );
  }
}