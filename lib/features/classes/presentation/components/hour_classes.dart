import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/utils/app_font.dart';

class HourClasses extends StatelessWidget {
  const HourClasses({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const PoppinsText(
          text: "11:30",
          fontSize: 13,
          fontWeight: FontWeight.w600,
        ),
        SizedBox(
          width: 4.w,
        ),
        Padding(
          padding: EdgeInsets.only(top: 4.h),
          child: const PoppinsText(
            text: "13:30",
            fontSize: 10,
            fontWeight: FontWeight.w600,
            color: AppColor.kGrayColor,
          ),
        ),
      ],
    );
  }
}
