import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_font.dart';
import '../../../../core/utils/app_icon.dart';
import '../../../../core/utils/app_string.dart';

class CardClasses extends StatelessWidget {
  final Color? color;
  const CardClasses({super.key, this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 258.h,
      height: 75.h,
      decoration: BoxDecoration(
        color:color,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const PoppinsText(
                  text: "Mathematics",
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
                SizedBox(
                  height: 16.h,
                ),
                Row(
                  children: [
                    Container(
                      width: 24.w,
                      height: 24.h,
                      decoration: BoxDecoration(
                        color: AppColor.khomeListColor5,
                        borderRadius: BorderRadius.circular(100.r),
                      ),
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    const PoppinsText(
                      text: "Waleed Soliman",
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: AppColor.khomeListColor6,
                    ),
                  ],
                ),
              ],
            ),
            Container(
                padding: EdgeInsets.symmetric(vertical: 5.h),
                width: 50.w,
                height: 50.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                  color: AppColor.kclassesAttendedColor,
                ),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      SvgPicture.asset(AppIcon.kdone),
                      const PoppinsText(
                        text: AppString.attended,
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                        color: AppColor.kBgColor,
                      ),
                    ]))
          ],
        ),
      ),
    );
  }
}
