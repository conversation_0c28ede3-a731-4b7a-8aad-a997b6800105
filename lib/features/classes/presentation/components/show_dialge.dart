import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/utils/app_icon.dart';
import '../../../../core/utils/app_string.dart';

class ShowDialoge extends StatelessWidget {
  const ShowDialoge({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      titlePadding: EdgeInsets.symmetric(horizontal: 18.h, vertical: 16.h),
      actionsPadding: EdgeInsets.symmetric(horizontal: 18.h, vertical: 20.h),
      contentPadding: EdgeInsets.symmetric(
          horizontal: 18.h, vertical: 8.h), // تعيين الهامش الداخلي للمحتوى
      backgroundColor: AppColor.kBgColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const PoppinsText(
              text: "8 Saturday", fontSize: 19, fontWeight: FontWeight.w700),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            width: 50.0,
            height: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.0),
              color: AppColor.kclassesAttendedColor,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SvgPicture.asset(AppIcon.kdone),
                const PoppinsText(
                  text: AppString.attended,
                  fontSize: 8,
                  fontWeight: FontWeight.w500,
                  color: AppColor.kBgColor,
                ),
              ],
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const PoppinsText(
              text: "Mathematics", fontSize: 16, fontWeight: FontWeight.w500),
          const SizedBox(height: 10.0),
          Row(
            children: [
              Container(
                width: 24.0,
                height: 24.0,
                decoration: BoxDecoration(
                  color: AppColor.khomeListColor5,
                  borderRadius: BorderRadius.circular(100.0),
                ),
              ),
              const SizedBox(width: 4.0),
              const PoppinsText(
                text: "Waleed Soliman",
                fontSize: 10,
                fontWeight: FontWeight.w400,
                color: AppColor.kBlackColor,
              ),
            ],
          ),
          const SizedBox(height: 10.0),
          Row(
            children: [
              SvgPicture.asset(AppIcon.khour),
              const SizedBox(width: 8.0),
              const PoppinsText(
                text: "11:30 - 13:30",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColor.kBlackColor,
              ),
            ],
          ),
          const SizedBox(height: 10.0),
          Row(
            children: [
              SvgPicture.asset(AppIcon.klocation),
              const SizedBox(width: 8.0),
              const PoppinsText(
                text: "City, Street name, building no.",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColor.kBlackColor,
              ),
            ],
          ),
          const SizedBox(height: 10.0),
          Row(
            children: [
              SvgPicture.asset(AppIcon.kbook),
              const SizedBox(width: 8.0),
              const Row(
                children: [
                  PoppinsText(
                    text: "Homework: ",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColor.kBlackColor,
                  ),
                  PoppinsText(
                    text: "From Page 23 to 25",
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColor.kBlackColor,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
      actions: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            color: AppColor.kPrimaryColor,
          ),
          child: Center(
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const PoppinsText(
                text: "Got it",
                color: AppColor.kBgColor,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
    ;
  }
}
