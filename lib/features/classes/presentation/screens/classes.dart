import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/classes/presentation/components/calender.dart';
import 'package:edus_student_ms/features/classes/presentation/components/classes_list.dart';
import 'package:edus_student_ms/core/widgets/title_main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Classes extends StatefulWidget {
  const Classes({Key? key}) : super(key: key);

  @override
  State<Classes> createState() => _ClassesState();
}

class _ClassesState extends State<Classes> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverPadding(
              padding: EdgeInsets.symmetric(vertical: 17.h, horizontal: 0.w),
              sliver: SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TitleMain(title: AppString.classes, horizontal: 16.w),
                    <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
                    const CalendarWidget(),
                    SizedBox(height: 16.h),
                    const ClassesList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
