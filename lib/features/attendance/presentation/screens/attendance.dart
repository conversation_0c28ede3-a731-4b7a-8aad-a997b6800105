import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/core/widgets/title_main.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/tab_bar_attendance.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Attendance extends StatefulWidget {
  const Attendance({super.key});

  @override
  State<Attendance> createState() => _AttendanceState();
}

class _AttendanceState extends State<Attendance> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TitleMain(title: AppString.attendance),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: const Tab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>()),
        ),
      ),
    );
  }
}
