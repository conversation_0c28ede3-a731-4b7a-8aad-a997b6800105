import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class QrCardInfo extends StatelessWidget {
  const QrCardInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
          height: 106.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              border: Border.all(color: AppColor.kGrayColor)),
          child: Padding(
            padding: EdgeInsets.all(16.h),
            child: const Column(children: [
              Row(children: [
                PoppinsText(
                  text: "Name : ",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),
                PoppinsText(
                  text: "Waleed Soliman",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                )
              ]),
              Row(children: [
                PoppinsText(
                  text: "Phone Number : ",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),
                PoppinsText(
                  text: "01028840504",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                )
              ]),
              Row(children: [
                PoppinsText(
                  text: "Address : ",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),
                PoppinsText(
                  text: "State, City, Street name",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                )
              ])
            ]),
          ),
        );
  }
}