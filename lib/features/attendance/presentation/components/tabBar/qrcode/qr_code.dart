import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/qrcode/qr_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class QrCode extends StatelessWidget {
  const QrCode({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 24.h,
        ),
        Image.asset(
          AppIcon.kqrCode,
          height: 343.h,
        ),
        SizedBox(
          height: 24.h,
        ),
        const QrCardInfo(),
      ],
    );
  }
}
