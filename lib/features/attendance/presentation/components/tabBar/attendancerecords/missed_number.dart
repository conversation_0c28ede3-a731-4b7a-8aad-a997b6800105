import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MissedNumber extends StatelessWidget {
  const MissedNumber({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: Color.fromRGBO(255, 206, 203, 1),
      ),
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: Row(
        children: [
          Container(
            width: 15.w,
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(5.r),
                  bottomLeft: Radius.circular(5.r)),
              color: Color.fromRGBO(255, 59, 48, 1),
            ),
          ),
          SizedBox(width: 8.w),
          Container(
            decoration: BoxDecoration(),
            padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '02',
                  textAlign: TextAlign.center,
                  style: AppFont.kFontPoppinsStyle
                      .copyWith(fontSize: 40.sp, fontWeight: FontWeight.w600),
                ),
                SizedBox(width: 8.w),
                Text(
                  'Missed',
                  textAlign: TextAlign.center,
                  style: AppFont.kFontPoppinsStyle
                      .copyWith(fontSize: 17.sp, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
