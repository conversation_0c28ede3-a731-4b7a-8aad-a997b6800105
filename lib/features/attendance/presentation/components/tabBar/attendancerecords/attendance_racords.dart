import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/attendancerecords/attended_number.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/attendancerecords/calender_attendace.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/attendancerecords/missed_number.dart';
import 'package:edus_student_ms/features/classes/presentation/components/calender.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AttendaceRecords extends StatelessWidget {
  const AttendaceRecords({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Sized<PERSON>ox(
          height: 24.h,
        ),
        const CalendarWidgetAttendance(),
        Sized<PERSON>ox(
          height: 16.h,
        ),
        Padding(
          padding: EdgeInsets.symmetric(vertical: 4.h),
          child: <PERSON>um<PERSON>(
            children: [
              AttendedNumber(),
              <PERSON><PERSON><PERSON><PERSON>(
                height: 8.h
              ),
              MissedNumber()
            ],
          ),
        )
      ],
    );
  }
}
