import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

class CalendarWidgetAttendance extends StatefulWidget {
  const CalendarWidgetAttendance({super.key});

  @override
  State<CalendarWidgetAttendance> createState() =>
      _CalendarWidgetAttendanceState();
}

class _CalendarWidgetAttendanceState extends State<CalendarWidgetAttendance> {
  late DateTime _selectedDate;
  late DateTime _currentDate;

  @override
  void initState() {
    super.initState();
    _currentDate = DateTime.now();
    _selectedDate = DateTime.now(); // Set initial selected date to current date
    print(
        'Selected Date: $_selectedDate'); // Print selected date for verification
  }

  List<DateTime> _getDaysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final lastDay = DateTime(month.year, month.month + 1, 0);
    return List.generate(
      lastDay.day,
      (index) => DateTime(month.year, month.month, index + 1),
    );
  }

  void _handleDateSelection(DateTime selectedDay) {
    setState(() {
      _selectedDate = selectedDay; // Update selected date
    });
    print(
        'Selected Date: $_selectedDate'); // Print selected date for verification
  }

  void _prevMonth() {
    setState(() {
      _currentDate = DateTime(_currentDate.year, _currentDate.month - 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _currentDate = DateTime(_currentDate.year, _currentDate.month + 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    final days = _getDaysInMonth(_currentDate);
    final firstDayOfMonth = days.first.weekday;

    return Container(
      height: 344.h,
      padding: EdgeInsets.all(16.h),
      decoration: BoxDecoration(
        color: AppColor.kBgColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat.yMMMM().format(_currentDate),
                style: AppFont.kFontPoppinsStyle
                    .copyWith(fontSize: 20.sp, fontWeight: FontWeight.w600),
              ),
              Row(
                children: [
                  IconButton(
                    icon: SvgPicture.asset(AppIcon.karrowLeft),
                    onPressed: _prevMonth,
                  ),
                  IconButton(
                    icon: SvgPicture.asset(AppIcon.karrowRight),
                    onPressed: _nextMonth,
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(7, (index) {
              return Expanded(
                child: Center(
                  child: Text(
                    DateFormat.E().format(DateTime(2021, 1, index + 1)),
                    style: AppFont.kFontPoppinsStyle
                        .copyWith(fontSize: 14.sp, fontWeight: FontWeight.w600),
                  ),
                ),
              );
            }),
          ),
          SizedBox(height: 8.h),
          Expanded(
            child: GridView.builder(
              itemCount: days.length + firstDayOfMonth - 1,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                childAspectRatio: 1.0,
              ),
              itemBuilder: (context, index) {
                if (index < firstDayOfMonth - 1) {
                  return Container();
                }
                final day = days[index - (firstDayOfMonth - 1)];
                final isSelected = day == _selectedDate;
                final isToday = day.day == DateTime.now().day &&
                    day.month == DateTime.now().month &&
                    day.year == DateTime.now().year;
                final isCurrentDateSelected =
                    _selectedDate.day == DateTime.now().day &&
                        _selectedDate.month == DateTime.now().month &&
                        _selectedDate.year == DateTime.now().year;

                return GestureDetector(
                  onTap: () => _handleDateSelection(day),
                  child: Container(
                    margin: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: AppColor.kattendedCalendarBox, width: 0.5.h),
                      color: (isSelected || (isToday && isCurrentDateSelected))
                          ? AppColor.kPrimaryColor
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                    child: Center(
                      child: Text(
                        '${day.day}',
                        style: AppFont.kFontPoppinsStyle.copyWith(
                            color: (isSelected ||
                                    (isToday && isCurrentDateSelected))
                                ? AppColor.kBgColor
                                : AppColor.kBlackColor,
                            fontSize: 14.sp,
                            fontWeight: (isSelected ||
                                    (isToday && isCurrentDateSelected))
                                ? FontWeight.w600
                                : FontWeight.w400),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
