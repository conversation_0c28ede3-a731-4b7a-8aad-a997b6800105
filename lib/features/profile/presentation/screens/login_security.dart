import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/core/widgets/input/password_input.dart';
import 'package:edus_student_ms/features/profile/presentation/components/loginSecurity/card_social.dart';
import 'package:edus_student_ms/features/profile/presentation/components/personalInformation/center_text_personal.dart';
import 'package:edus_student_ms/features/profile/presentation/components/personalInformation/save_botton.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:form_validator/form_validator.dart';

class LoginAndSecurity extends StatefulWidget {
  const LoginAndSecurity({super.key});

  @override
  State<LoginAndSecurity> createState() => _LoginAndSecurityState();
}

class _LoginAndSecurityState extends State<LoginAndSecurity> {
  TextEditingController oldPasswordController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmedNewPasswordController =
      TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: Padding(
            padding: EdgeInsets.only(left: 5.w), // تعديل المسافة حسب الحاجة
            child: IconButton(
              icon: SvgPicture.asset(
                  AppIcon.kleftArrowPersonal), // استبدل بمسار أيقونتك الخاصة
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
          leadingWidth: 40.h,
          title: const PoppinsText(
            textAlign: TextAlign.start,
            text: AppString.login,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          )),
      body: SafeArea(
        child: SingleChildScrollView(
            child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Column(
            children: [
              const CenterTextPersonal(
                text: AppString.password,
              ),
              SizedBox(height: 8.h),
              PasswordInput(
                  horizontal: 0,
                  icon: AppIcon.kLockIcon,
                  hint: AppString.oldPass,
                  inputType: TextInputType.visiblePassword,
                  controller: oldPasswordController,
                  validator: ValidationBuilder().minLength(8).build()),
              SizedBox(height: 8.h),
              PasswordInput(
                  horizontal: 0,
                  icon: AppIcon.kLockIcon,
                  hint: AppString.newPass,
                  inputType: TextInputType.visiblePassword,
                  controller: newPasswordController,
                  validator: ValidationBuilder().minLength(8).build()),
              SizedBox(height: 8.h),
              PasswordInput(
                  horizontal: 0,
                  icon: AppIcon.kLockIcon,
                  hint: AppString.confirmPass,
                  inputType: TextInputType.visiblePassword,
                  controller: confirmedNewPasswordController,
                  validator: ValidationBuilder().minLength(8).build()),
              SizedBox(height: 16.h),
              const CenterTextPersonal(
                text: AppString.socialAccounts,
              ),
              SizedBox(height: 8.h),
              const CardSocial(
                  text1: "Google",
                  text2: "<EMAIL>",
                  text3: "disconnect",
                  assetName: AppIcon.klogoGoogle),
              SizedBox(height: 8.h),
              const CardSocial(
                  text1: "Apple",
                  text2: "Not connected",
                  text3: "Connect",
                  assetName: AppIcon.klogoApple),
              SizedBox(
                height: 210.h,
              ),
              SaveBotton(
                onTap: () {},
              ),
              SizedBox(
                height: 3.h,
              ),
            ],
          ),
        )),
      ),
    );
  }
}
