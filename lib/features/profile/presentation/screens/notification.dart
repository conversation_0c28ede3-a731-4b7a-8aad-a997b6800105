import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/profile/presentation/components/notification/center_card.dart';
import 'package:edus_student_ms/features/profile/presentation/components/personalInformation/center_text_personal.dart';
import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  bool isSwitched1 = true;
  bool isSwitched2 = true;
  bool isSwitched3 = true;
  bool isSwitched4 = true;
  bool isSwitched5 = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: Padding(
            padding: EdgeInsets.only(left: 5.w), // تعديل المسافة حسب الحاجة
            child: IconButton(
              icon: SvgPicture.asset(
                  AppIcon.kleftArrowPersonal), // استبدل بمسار أيقونتك الخاصة
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
          leadingWidth: 40.h,
          title: const PoppinsText(
            textAlign: TextAlign.start,
            text: AppString.notifications,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          )),
      body: SafeArea(
        child: SingleChildScrollView(
            child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Column(
            children: [
              SizedBox(
                height: 4.h,
              ),
              CenterCardNotification(
                text1: AppString.notifications1,
                text2: AppString.notifications2,
                child: Switch(
                  value: isSwitched1,
                  onChanged: (value) {
                    setState(() {
                      isSwitched1 = value;
                    });
                  },
                  activeTrackColor: AppColor.kgreen,
                  activeColor: AppColor.kBgColor,
                ),
              ),
              SizedBox(
                height: 24.h,
              ),
              const CenterTextPersonal(
                text: AppString.pushNotifications,
              ),
              SizedBox(
                height: 20.h,
              ),
              CenterCardNotification(
                text1: AppString.notifications3,
                child: Switch(
                  value: isSwitched2,
                  onChanged: (value) {
                    setState(() {
                      isSwitched2 = value;
                    });
                  },
                  activeTrackColor: AppColor.kgreen,
                  activeColor: AppColor.kBgColor,
                ),
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardNotification(
                text1: AppString.notifications4,
                child: Switch(
                  value: isSwitched3,
                  onChanged: (value) {
                    setState(() {
                      isSwitched3 = value;
                    });
                  },
                  activeTrackColor: AppColor.kgreen,
                  activeColor: AppColor.kBgColor,
                ),
              ),
              SizedBox(
                height: 24.h,
              ),
              const CenterTextPersonal(
                text: AppString.emailNotifications,
              ),
              SizedBox(
                height: 20.h,
              ),
              CenterCardNotification(
                text1: AppString.notifications5,
                child: Switch(
                  value: isSwitched4,
                  onChanged: (value) {
                    setState(() {
                      isSwitched4 = value;
                    });
                  },
                  activeTrackColor: AppColor.kgreen,
                  activeColor: AppColor.kBgColor,
                ),
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardNotification(
                text1: AppString.notifications6,
                child: Switch(
                  value: isSwitched5,
                  onChanged: (value) {
                    setState(() {
                      isSwitched5 = value;
                    });
                  },
                  activeTrackColor: AppColor.kgreen,
                  activeColor: AppColor.kBgColor,
                ),
              ),
            ],
          ),
        )),
      ),
    );
  }
}
