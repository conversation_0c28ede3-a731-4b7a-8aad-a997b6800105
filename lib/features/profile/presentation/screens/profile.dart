import 'package:edus_student_ms/core/config/app_routes.dart';
import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/core/widgets/title_main.dart';
import 'package:edus_student_ms/features/profile/presentation/components/card_profile_info.dart';
import 'package:edus_student_ms/features/profile/presentation/components/center_card.dart';
import 'package:edus_student_ms/features/profile/presentation/components/center_text.dart';
import 'package:edus_student_ms/features/profile/presentation/components/logout_botton_profile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class Profile extends StatefulWidget {
  const Profile({super.key});

  @override
  State<Profile> createState() => _ProfileState();
}

class _ProfileState extends State<Profile> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TitleMain(title: AppString.profile),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              children: [
                const CardProfileInfo(),
                SizedBox(height: 24.h),
                const CenterTextProfile(text: AppString.account),
                SizedBox(height: 20.h),
                CenterCard(
                    onTap: () {
                      Navigator.pushNamed(context, kPersonalInformation);
                    },
                    iconName: AppIcon.kperson,
                    text: AppString.personal,
                    verticalPaddingWedgit: 8.h,
                    horizontalPaddingWedgit: 10.h,
                    child: SvgPicture.asset(AppIcon.kprofileRightRow,
                        height: 8.h)),
                SizedBox(height: 8.h),
                CenterCard(
                    onTap: () {
                      Navigator.pushNamed(context, kLoginScurity);
                    },
                    iconName: AppIcon.kloginProfile,
                    text: AppString.login,
                    verticalPaddingWedgit: 8.h,
                    horizontalPaddingWedgit: 10.h,
                    child: SvgPicture.asset(AppIcon.kprofileRightRow,
                        height: 8.h)),
                SizedBox(height: 16.h),
                const CenterTextProfile(text: AppString.settings),
                SizedBox(height: 20.h),
                CenterCard(
                    onTap: () {
                      Navigator.pushNamed(context, knotification);
                    },
                    iconName: AppIcon.knotificationProfile,
                    text: AppString.notifications,
                    verticalPaddingWedgit: 8.h,
                    horizontalPaddingWedgit: 10.h,
                    child: SvgPicture.asset(AppIcon.kprofileRightRow,
                        height: 8.h)),
                SizedBox(height: 8.h),
                CenterCard(
                    onTap: () {},
                    iconName: AppIcon.klanguageProfile,
                    text: AppString.language,
                    verticalPaddingWedgit: 1.5.h,
                    horizontalPaddingWedgit: 10.h,
                    child: const PoppinsText(
                      color: AppColor.kGrayColor,
                      text: "English",
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    )),
                SizedBox(height: 8.h),
                CenterCard(
                    onTap: () {},
                    iconName: AppIcon.kthemeProfile,
                    text: AppString.theme,
                    verticalPaddingWedgit: 1.5.h,
                    horizontalPaddingWedgit: 10.h,
                    child: const PoppinsText(
                      color: AppColor.kGrayColor,
                      text: "Light",
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    )),
                SizedBox(height: 16.h),
                const CenterTextProfile(text: AppString.general),
                SizedBox(height: 20.h),
                CenterCard(
                    onTap: () {},
                    iconName: AppIcon.krateProfile,
                    text: AppString.rate,
                    verticalPaddingWedgit: 8.h,
                    horizontalPaddingWedgit: 10.h,
                    child: SvgPicture.asset(AppIcon.kprofileRightRow,
                        height: 8.h)),
                SizedBox(height: 8.h),
                CenterCard(
                    onTap: () {},
                    iconName: AppIcon.kshareProfile,
                    text: AppString.share,
                    verticalPaddingWedgit: 8.h,
                    horizontalPaddingWedgit: 10.h,
                    child: SvgPicture.asset(AppIcon.kprofileRightRow,
                        height: 8.h)),
                SizedBox(height: 8.h),
                CenterCard(
                    onTap: () {},
                    iconName: AppIcon.kprivacyrofile,
                    text: AppString.privacy,
                    verticalPaddingWedgit: 8.h,
                    horizontalPaddingWedgit: 10.h,
                    child: SvgPicture.asset(AppIcon.kprofileRightRow,
                        height: 8.h)),
                SizedBox(height: 8.h),
                CenterCard(
                    onTap: () {},
                    iconName: AppIcon.kprivacyrofile,
                    text: AppString.termes,
                    verticalPaddingWedgit: 8.h,
                    horizontalPaddingWedgit: 10.h,
                    child: SvgPicture.asset(AppIcon.kprofileRightRow,
                        height: 8.h)),
                SizedBox(height: 24.h),
                const LogoutProfile(),
                SizedBox(height: 94.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
