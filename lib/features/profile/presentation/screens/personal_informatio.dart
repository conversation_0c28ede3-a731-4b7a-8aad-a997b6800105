import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/profile/presentation/components/personalInformation/card_chosse_photo.dart';
import 'package:edus_student_ms/features/profile/presentation/components/personalInformation/center_card_info.dart';
import 'package:edus_student_ms/features/profile/presentation/components/personalInformation/center_text_personal.dart';
import 'package:edus_student_ms/features/profile/presentation/components/personalInformation/save_botton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class PersonalInformation extends StatefulWidget {
  const PersonalInformation({super.key});

  @override
  State<PersonalInformation> createState() => _PersonalInformationState();
}

class _PersonalInformationState extends State<PersonalInformation> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: Padding(
            padding: EdgeInsets.only(left: 5.w), // تعديل المسافة حسب الحاجة
            child: IconButton(
              icon: SvgPicture.asset(
                  AppIcon.kleftArrowPersonal), // استبدل بمسار أيقونتك الخاصة
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
          leadingWidth: 40.h,
          title: const PoppinsText(
            textAlign: TextAlign.start,
            text: AppString.personal,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          )),
      body: SafeArea(
        child: SingleChildScrollView(
            child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Column(
            children: [
              const CardChossePhoto(),
              SizedBox(
                height: 16.h,
              ),
              const CenterTextPersonal(
                text: AppString.studentInformation,
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalPerson),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.name,
                text2: "Waleed Mohamed",
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalEmail),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.email,
                text2: "<EMAIL>",
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalPhone),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.mobile,
                text2: "01028840504",
              ),
              SizedBox(
                height: 16.h,
              ),
              const CenterTextPersonal(
                text: AppString.address,
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                text1: AppString.state,
                text2: "Kafr al-Shikh",
                arrowDown: SvgPicture.asset(AppIcon.kpersonalArrowDown),
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                text1: AppString.city,
                text2: "Baltim",
                arrowDown: SvgPicture.asset(AppIcon.kpersonalArrowDown),
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                text1: AppString.address,
                text2: "Street name, building no.",
              ),
              SizedBox(
                height: 16.h,
              ),
              const CenterTextPersonal(
                text: AppString.fatherInformation,
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalPerson),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.name,
                text2: "Waleed Mohamed",
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalPhone),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.mobile,
                text2: "01028840504",
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalProfission),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.profession,
                text2: "Real State seller",
              ),
              SizedBox(
                height: 16.h,
              ),
              const CenterTextPersonal(
                text: AppString.motherInformation,
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalPerson),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.name,
                text2: "Name",
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalPhone),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.mobile,
                text2: "Phone number",
              ),
              SizedBox(
                height: 8.h,
              ),
              CenterCardInfo(
                // ignore: sort_child_properties_last
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 13.h, horizontal: 4.h),
                    child: SvgPicture.asset(AppIcon.kpersonalProfission),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
                text1: AppString.profession,
                text2: "House Wife",
              ),
              SizedBox(
                height: 16.h,
              ),
              SaveBotton(
                onTap: () {},
              ),
              SizedBox(
                height: 3.h,
              ),
            ],
          ),
        )),
      ),
    );
  }
}
