import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CardSocial extends StatelessWidget {
  final String text1;
  final String text2;
  final String text3;
  final String assetName;
  const CardSocial({super.key, required this.text1, required this.text2, required this.text3, required this.assetName});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 66.h,
      padding: EdgeInsets.all(8.h),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.all(8.h),
                  child: SvgPicture.asset(assetName),
                ),
                SizedBox(
                  width: 8.h,
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    PoppinsText(
                      text:text1 ,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    PoppinsText(
                      text:text2,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColor.kGrayColor,
                    )
                  ],
                )
              ],
            ),
            PoppinsText(
              text:text3,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppColor.kGrayColor,
            )
          ]),
    );
  }
}
