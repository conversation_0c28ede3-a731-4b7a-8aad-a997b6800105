import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_icon.dart';

class CardProfileInfo extends StatelessWidget {
  const CardProfileInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
                padding: EdgeInsets.all(8.h),
                height: 90.h,
                decoration: BoxDecoration(
                    border: Border.all(
                        color: AppColor.kfessbordercolor, width: 0.5.w),
                    borderRadius: BorderRadius.circular(10.r)),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Image.asset(
                              AppIcon.kprofile,
                              height: 74.h,
                            ),
                            SizedBox(width: 9.w),
                            const Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                PoppinsText(
                                  text: "Waleed Mohamed Soliman",
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                                PoppinsText(
                                  text: "01028840504",
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: AppColor.kGrayColor,
                                ),
                                PoppinsText(
                                  text: "State, City, Street name",
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: AppColor.kGrayColor,
                                )
                              ],
                            )
                          ]),
                      GestureDetector(
                          onTap: () {},
                          child: Padding(
                            padding: EdgeInsets.all(5.33.h),
                            child: SvgPicture.asset(AppIcon.kqrCodeSvg),
                          ))
                    ]));
  }
}