import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CenterTextProfile extends StatelessWidget {
  final String text;
  const CenterTextProfile({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 4.h),
          child: PoppinsText(
            textAlign: TextAlign.start,
            text: text,
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: AppColor.kGrayColor,
          ),
        ),
      ],
    );
  }
}
