import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CenterCardNotification extends StatelessWidget {
  final String text1;
  final String? text2;
  final Widget child;
  const CenterCardNotification(
      {super.key, required this.text1, this.text2, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 260.w,
            child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  text2 == null
                      ? Padding(
                          padding: EdgeInsets.only(top: 10.h),
                          child: PoppinsText(
                            textAlign: TextAlign.start,
                            text: text1,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      : PoppinsText(
                          textAlign: TextAlign.start,
                          text: text1,
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                        ),
                  PoppinsText(
                    textAlign: TextAlign.start,
                    text: text2 ?? "",
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColor.kGrayColor,
                  )
                ]),
          ),
          Container(child: child)
        ],
      ),
    );
  }
}
