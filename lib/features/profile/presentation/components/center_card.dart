import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';


class CenterCard extends StatelessWidget {
  final void Function() onTap;
  final String iconName;
  final String text;
  final double verticalPaddingWedgit;
  final double horizontalPaddingWedgit;
  final Widget child;

  const CenterCard(
      {super.key,
      required this.onTap,
      required this.iconName,
      required this.text,
      required this.verticalPaddingWedgit,
      required this.horizontalPaddingWedgit,
      required this.child});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
          padding: EdgeInsets.all(10.h),
          height: 44.h,
          decoration: BoxDecoration(
              border:
                  Border.all(color: AppColor.kfessbordercolor, width: 0.5.w),
              borderRadius: BorderRadius.circular(10.r)),
          child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 3.h, horizontal: 4.h),
                        child: SvgPicture.asset(
                          iconName,
                          height: 18.h,
                        ),
                      ),
                      SizedBox(width: 10.w),
                      PoppinsText(
                        text: text,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ]),
                Padding(
                  padding: EdgeInsets.symmetric(
                      vertical: verticalPaddingWedgit,
                      horizontal: horizontalPaddingWedgit),
                  child: child,
                )
              ])),
    );
  }
}
