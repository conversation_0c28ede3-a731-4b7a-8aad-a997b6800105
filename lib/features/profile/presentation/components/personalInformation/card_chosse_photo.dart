import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CardChossePhoto extends StatelessWidget {
  const CardChossePhoto({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppIcon.kchossePhoto),
        Sized<PERSON>ox(
          width: 7.w,
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PoppinsText(
              text: "Profile Photo",
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            PoppinsText(
              textAlign: TextAlign.start,
              text:
                  "Please make sure your photo is clear \n and your face shown",
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: AppColor.kGrayColor,
            )
          ],
        )
      ],
    );
  }
}
