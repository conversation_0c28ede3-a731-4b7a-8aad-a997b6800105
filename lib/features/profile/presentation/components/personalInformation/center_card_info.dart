import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CenterCardInfo extends StatelessWidget {
  final List<Widget>? children;
  final String text1;
  final String text2;
  final Widget? arrowDown;
  const CenterCardInfo(
      {super.key,
      this.children,
      required this.text1,
      required this.text2,
      this.arrowDown});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 54.h,
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(children: children ?? []),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    PoppinsText(
                      text: text1,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: AppColor.kGrayColor,
                    ),
                    PoppinsText(
                      text: text2,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    )
                  ],
                )
              ]),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 8.h),
            child: arrowDown ?? const Text(""),
          ),
        ],
      ),
    );
  }
}
