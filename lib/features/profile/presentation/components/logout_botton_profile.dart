import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class LogoutProfile extends StatelessWidget {
  const LogoutProfile({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        height: 44.h,
        decoration: BoxDecoration(
            color: AppColor.klogout, borderRadius: BorderRadius.circular(10.r)),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.all(4.h),
                child: SvgPicture.asset(AppIcon.klogoutProfile),
              ),
              SizedBox(
                width: 10.w,
              ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 1.5.h),
                child: const PoppinsText(
                  text: AppString.logOut,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColor.kBgColor,
                ),
              )
            ]),
      ),
    );
  }
}
