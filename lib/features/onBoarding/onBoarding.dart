import 'dart:async';

import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../core/config/app_routes.dart';
import '../../core/utils/app_color.dart';
import '../../core/utils/app_font.dart';
import '../../core/widgets/buttons/login_button.dart';
import '../../core/widgets/buttons/sign_up_button.dart';

class OnBoarding extends StatefulWidget {
  const OnBoarding({Key? key}) : super(key: key);

  @override
  State<OnBoarding> createState() => _OnBoardingState();
}

class _OnBoardingState extends State<OnBoarding> {
  bool isLastPage = false;
  int _currentPage = 0;
  Timer? timer;
  PageController controller = PageController(
    initialPage: 0,
  );

  @override
  void initState() {
    super.initState();
    timer = Timer.periodic(const Duration(seconds: 3), (Timer timer) {
      if (_currentPage < 2) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }

      controller.animateToPage(_currentPage,
          duration: const Duration(milliseconds: 350), curve: Curves.easeIn);
    });
  }

  @override
  // ignore: must_call_super
  void dispose() {
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColor.kBgColor,
        body: SafeArea(
            child: Stack(children: [
          Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
              child: SmoothPageIndicator(
                  onDotClicked: (index) => controller.animateToPage(index,
                      duration: const Duration(milliseconds: 400),
                      curve: Curves.easeInOut),
                  controller: controller,
                  count: 3,
                  effect: CustomizableEffect(
                      activeDotDecoration: DotDecoration(
                          height: 5.h,
                          borderRadius: BorderRadius.circular(30.r),
                          color: AppColor.kPrimaryColor,
                          width: 105.w),
                      dotDecoration: DotDecoration(
                          height: 5.h,
                          borderRadius: BorderRadius.circular(30.r),
                          color: AppColor.kSecondaryColor,
                          width: 105.w)))),
          Padding(
              padding: EdgeInsets.only(bottom: 160.h),
              child: PageView(
                  controller: controller,
                  onPageChanged: (index) {
                    setState(() {
                      isLastPage = index == 2;
                    });
                  },
                  children: [
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 266.h),
                          SizedBox(
                              width: 344.w,
                              height: 149.h,
                              child: Image.asset(AppIcon.kOnBoarding1)),
                          const Spacer(),
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 6.w),
                              child: const PoppinsText(
                                  text: AppString.welcomeToEdusStudentMS,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600)),
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 26.w),
                              child: const PoppinsText(
                                  text: AppString.theEasiestWay,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xff5F5F5F)))
                        ]),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 205.h),
                          SizedBox(
                              width: 344.w,
                              height: 210.h,
                              child: Image.asset(AppIcon.kOnBoarding2)),
                          const Spacer(),
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: const PoppinsText(
                                  text: AppString.quickAndEasyAttendance,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600)),
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 26.w),
                              child: const PoppinsText(
                                  text: AppString
                                      .sayGoodbyeToManualAttendanceTracking,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xff5F5F5F)))
                        ]),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 171.h),
                          SizedBox(
                              width: 339.w,
                              height: 244.h,
                              child: Image.asset(AppIcon.kOnBoarding3)),
                          const Spacer(),
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: const PoppinsText(
                                  text: AppString.stayOnTrack,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600)),
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 26.w),
                              child: const PoppinsText(
                                  text: AppString.keepUpWithYourClasses,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xff5F5F5F)))
                        ])
                  ])),
          Column(children: [
            const Spacer(),
            Center(child: SignUpButton(onPress: () {
              Navigator.pushNamed(context, kSignup);
            })),
            SizedBox(height: 18.h),
            Center(
                child: LogInButton(
                    onPress: () {
                      Navigator.pushNamed(context, kLogin);
                    },
                    buttonColor: AppColor.kBgColor,
                    textColor: AppColor.kPrimaryColor)),
            SizedBox(height: 30.h)
          ])
        ])));
  }
}
