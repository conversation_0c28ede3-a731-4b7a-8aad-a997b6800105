import 'dart:async';
import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/utils/enums.dart';
import '../../domain/entities/home_data.dart';
import '../../domain/usecases/get_home_data_usecase.dart';

part 'home_event.dart';

part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetHomeDataUseCase _getHomeDataUseCase;

  HomeBloc(this._getHomeDataUseCase) : super(const HomeState()) {
    on<GetHomeDataEvent>(_getHomeDataEvent);
  }

  Future<void> _getHomeDataEvent(
      GetHomeDataEvent event, Emitter<HomeState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final result = await _getHomeDataUseCase(event.language);

    result.fold(
      (failure) => emit(state.copyWith(
        status: RequestStatus.error,
        error: failure.toString(),
      )),
      (homeData) => emit(state.copyWith(
        status: RequestStatus.success,
        homeData: homeData,
      )),
    );
  }

  // FutureOr<void> _doLoginEvent(
  //     DoLoginEvent event, Emitter<HomeState> emit) async {
  //   emit(state.copyWith(status: RequestStatus.loading));

  //   final res = await _doLoginUseCase(event.params);
  //   res.fold(
  //       (l) => emit(state.copyWith(
  //           loginData: null,
  //           status: RequestStatus.error,
  //           error: l.error.message)), (r) {
  //     if (r.status == 'fail') {
  //       emit(state.copyWith(
  //           loginData: null, status: RequestStatus.error, error: r.message));
  //     } else {
  //       emit(state.copyWith(
  //           status: RequestStatus.success, error: null, loginData: r));
  //     }
  //   });
  // }

}
