import 'dart:async';
import 'dart:developer';

import 'package:edus_student_ms/features/Home/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/Home/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/Home/domain/usecases/get_signup_data_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/utils/enums.dart';
import '../../domain/entities/login/login_data.dart';

part 'home_event.dart';

part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final DoSignUpUseCase _doSignUpUseCase;

  HomeBloc(
     this._doSignUpUseCase)
      : super(const HomeState()) {
    // on<DoLoginEvent>(_doLoginEvent);
   
  }

  // FutureOr<void> _doLoginEvent(
  //     DoLoginEvent event, Emitter<HomeState> emit) async {
  //   emit(state.copyWith(status: RequestStatus.loading));

  //   final res = await _doLoginUseCase(event.params);
  //   res.fold(
  //       (l) => emit(state.copyWith(
  //           loginData: null,
  //           status: RequestStatus.error,
  //           error: l.error.message)), (r) {
  //     if (r.status == 'fail') {
  //       emit(state.copyWith(
  //           loginData: null, status: RequestStatus.error, error: r.message));
  //     } else {
  //       emit(state.copyWith(
  //           status: RequestStatus.success, error: null, loginData: r));
  //     }
  //   });
  // }

}
