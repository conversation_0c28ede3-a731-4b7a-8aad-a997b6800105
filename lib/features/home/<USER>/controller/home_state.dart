part of 'home_bloc.dart';

class HomeState extends Equatable {
  final RequestStatus status;
  final String error;
  final HomeData? homeData;

  const HomeState({
    this.status = RequestStatus.initial,
    this.error = "",
    this.homeData,
  });

  HomeState copyWith({
    RequestStatus? status,
    String? error,
    HomeData? homeData,
  }) {
    return HomeState(
      status: status ?? this.status,
      error: error ?? this.error,
      homeData: homeData ?? this.homeData,
    );
  }

  @override
  List<Object?> get props => [status, error, homeData];
}
