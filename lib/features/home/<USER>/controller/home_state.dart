part of 'home_bloc.dart';

class HomeState extends Equatable {
  final RequestStatus status;
  final String error;
  // final LoginData? loginData;
  // final SignUpData? signUpData;

  const HomeState(
      {
      // this.loginData,
      // this.signUpData,
      this.status = RequestStatus.initial,
      this.error = ""});

  HomeState copyWith(
      {
      RequestStatus? status,
      String? error,
      LoginData? loginData,
      SignUpData? signUpData,
      }) {
    return HomeState(
        // loginData: loginData ?? loginData,
        // signUpData: signUpData ?? signUpData,
        status: status ?? this.status,
        error: error ?? this.error);
  }

  @override
  List<Object> get props => [status];
}
