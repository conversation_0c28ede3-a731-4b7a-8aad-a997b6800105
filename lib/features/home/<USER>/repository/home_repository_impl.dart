import 'package:dartz/dartz.dart';
import 'package:edus_student_ms/features/Home/data/models/signupModel/signup_error_model.dart';
import 'package:edus_student_ms/features/Home/domain/entities/signup/signup_data.dart';
import 'package:edus_student_ms/features/Home/domain/usecases/get_signup_data_usecase.dart';
import 'package:edus_student_ms/features/home/<USER>/datasource/Home_remote_datasource.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/services/service_locator.dart';
import '../../../../core/services/token_service/token_storage.dart';
import '../../domain/entities/login/login_data.dart';
import '../../domain/entities/login/login_error.dart';
import '../../domain/repository/home_repository.dart';
import '../models/loginModel/login_error_model.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource remoteDataSource;

  HomeRepositoryImpl(this.remoteDataSource);

  // @override
  // Future<Either<Failure<LoginError>, LoginData>> doLogin(LoginParams params) async {
  //   try {
  //     final result = await remoteDataSource.doLogin(params);
  //     sl<TokenStorage>().storeToken(result.userData?.token??"");
  //     sl<TokenStorage>().storeUserId(result.userData?.userId??0);
  //     print(result.userData?.token);
  //     return Right(result);
  //   } on ServerException<LoginErrorModel> catch (e) {
  //     return Left(ServerFailure(e.message));
  //   }
  // }

}
