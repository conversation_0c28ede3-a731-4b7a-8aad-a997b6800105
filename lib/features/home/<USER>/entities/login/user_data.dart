import 'package:equatable/equatable.dart';

class UserData extends Equatable {
  final int userId;
  final String fullName;
  final String email;
  final String phone;

  final String token;

  const UserData({
    required this.userId,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.token,
  });

  @override
  // TODO: implement props
  List<Object?> get props => [
        userId,
        fullName,
        email,
        phone,
        token,
      ];
}
