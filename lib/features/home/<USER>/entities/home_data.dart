import 'package:equatable/equatable.dart';

class HomeData extends Equatable {
  final String status;
  final List<UpcomingClass> upcomingClasses;
  final List<Homework> homeworks;

  const HomeData({
    required this.status,
    required this.upcomingClasses,
    required this.homeworks,
  });

  @override
  List<Object?> get props => [status, upcomingClasses, homeworks];
}

class UpcomingClass extends Equatable {
  final String classTitle;
  final String classTime;
  final String teacherName;

  const UpcomingClass({
    required this.classTitle,
    required this.classTime,
    required this.teacherName,
  });

  @override
  List<Object?> get props => [classTitle, classTime, teacherName];
}

class Homework extends Equatable {
  final String classTitle;
  final String assignmentTitle;
  final String teacherName;

  const Homework({
    required this.classTitle,
    required this.assignmentTitle,
    required this.teacherName,
  });

  @override
  List<Object?> get props => [classTitle, assignmentTitle, teacherName];
}
