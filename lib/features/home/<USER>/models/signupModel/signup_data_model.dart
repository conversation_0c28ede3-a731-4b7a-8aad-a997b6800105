import 'dart:convert';

import 'package:edus_student_ms/features/auth/domain/entities/signup/signup_data.dart';

SignUpModel signUpModelFromJson(String str) =>
    SignUpModel.fromJson(json.decode(str));

class SignUpModel extends SignUpData {

  const SignUpModel( {
    required super.status,
    required super.message,
  });

  factory SignUpModel.fromJson(Map<String, dynamic> json) {
    return SignUpModel(
      status: json["status"],
      message: json["message"],

    );
  }
}
