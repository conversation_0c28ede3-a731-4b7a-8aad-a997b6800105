import 'dart:convert';

import '../../../domain/entities/login/user_data.dart';



 UserDataModel loginModelFromJson(String str) =>
     UserDataModel.fromJson(json.decode(str));

class UserDataModel extends UserData {

  const UserDataModel( {
    required super.userId,
    required super.fullName,
    required super.email,
    required super.phone,
    required super.token,
   
  });

  factory UserDataModel.fromJson(Map<String, dynamic> json) {
    return UserDataModel(
    userId: json['userId'] ?? 0,
      fullName: json['student_name']??"",
      email: json['email'] ??"",
      phone: json['contact_number'] ??"",
      token: json['token'] ?? "",
    );
  }
}