import 'dart:convert';


import 'package:edus_student_ms/features/auth/data/models/loginModel/user_data_model.dart';

import '../../../domain/entities/login/login_data.dart';

LoginModel loginModelFromJson(String str) =>
    LoginModel.fromJson(json.decode(str));

class LoginModel extends LoginData {

  const LoginModel( {
    required super.status,
    required super.message,
    required super.userData,
  });

  factory LoginModel.fromJson(Map<String, dynamic> json) {
    return LoginModel(
      status: json["status"],
      message: json["message"],
     userData: json["data"] != null
          ? UserDataModel.fromJson(json["data"])  
          : null, // Handle null case for data
    );
  }
}