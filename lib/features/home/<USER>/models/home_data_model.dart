import 'dart:convert';
import '../../domain/entities/home_data.dart';

HomeDataModel homeDataModelFromJson(String str) =>
    HomeDataModel.fromJson(json.decode(str));

class HomeDataModel extends HomeData {
  const HomeDataModel({
    required super.status,
    required super.upcomingClasses,
    required super.homeworks,
  });

  factory HomeDataModel.fromJson(Map<String, dynamic> json) {
    return HomeDataModel(
      status: json['status'] ?? '',
      upcomingClasses: json['data'] != null && json['data']['upcomingclasses'] != null
          ? (json['data']['upcomingclasses'] as List)
              .map((x) => UpcomingClassModel.fromJson(x))
              .toList()
          : [],
      homeworks: json['data'] != null && json['data']['homeworks'] != null
          ? (json['data']['homeworks'] as List)
              .map((x) => HomeworkModel.fromJson(x))
              .toList()
          : [],
    );
  }
}

class UpcomingClassModel extends UpcomingClass {
  const UpcomingClassModel({
    required super.classTitle,
    required super.classTime,
    required super.teacherName,
  });

  factory UpcomingClassModel.fromJson(Map<String, dynamic> json) {
    return UpcomingClassModel(
      classTitle: json['ClassTitle'] ?? '',
      classTime: json['ClassTime'] ?? '',
      teacherName: json['TeacherName'] ?? '',
    );
  }
}

class HomeworkModel extends Homework {
  const HomeworkModel({
    required super.classTitle,
    required super.assignmentTitle,
    required super.teacherName,
  });

  factory HomeworkModel.fromJson(Map<String, dynamic> json) {
    return HomeworkModel(
      classTitle: json['ClassTitle'] ?? '',
      assignmentTitle: json['AssignmentTitle'] ?? '',
      teacherName: json['TeacherName'] ?? '',
    );
  }
}
