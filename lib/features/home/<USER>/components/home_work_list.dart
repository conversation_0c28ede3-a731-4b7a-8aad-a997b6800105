import 'dart:math';

import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeWorkList extends StatefulWidget {
  const HomeWorkList({super.key});

  @override
  State<HomeWorkList> createState() => _HomeWorkListState();
}

class _HomeWorkListState extends State<HomeWorkList> {
  final List<Color> colors = [
    AppColor.khomeListColor1.withOpacity(0.5),
    AppColor.khomeListColor2.withOpacity(0.5),
    AppColor.khomeListColor3.withOpacity(0.5),
    AppColor.khomeListColor4.withOpacity(0.5),
    AppColor.khomeListColor5.withOpacity(0.5),
  ];

  final Random random = Random();

  late List<Color> itemColors;

  @override
  void initState() {
    super.initState();
    // Generate a list of colors once and store it
    itemColors =
        List.generate(50, (index) => colors[random.nextInt(colors.length)]);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 124.h,
      child: ListView.builder(
          padding: EdgeInsets.only(left: 16.w),
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          itemCount: 10,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: EdgeInsets.only(right: 8.w),
              child: Container(
                width: 260.w,
                height: 124.h,
                decoration: BoxDecoration(
                    color: itemColors[index],
                    borderRadius: BorderRadius.circular(10.r)),
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const PoppinsText(
                        text: "Mathematics",
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      const PoppinsText(
                        text: "Homework : from page 23 to 25",
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      Row(
                        children: [
                          Container(
                            width: 24.w,
                            height: 24.h,
                            decoration: BoxDecoration(
                                color: Colors.greenAccent,
                                borderRadius: BorderRadius.circular(100.r)),
                          ),
                          SizedBox(
                            width: 4.w,
                          ),
                          const PoppinsText(
                            text: "Waleed Soliman",
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: Color(0xff576B74),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            );
          }),
    );
  }
}
