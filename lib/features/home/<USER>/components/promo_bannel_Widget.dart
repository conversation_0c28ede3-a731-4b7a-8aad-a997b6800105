import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EduspromotbannelWidget extends StatelessWidget {
  const EduspromotbannelWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Container(
        height: 185.w,
        width: 343.w,
        decoration: BoxDecoration(
          color: AppColor.kPrimaryColor,
          borderRadius: BorderRadius.circular(5.r), // حواف دائرية
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 16.w),
              SizedBox(
                width: 80,
                height: 20,
                child: Stack(
                  children: [
                    Positioned(
                      top: 0,
                      left: 0,
                      child: SvgPicture.asset(
                        AppIcon.kedus1,
                      ),
                    ),
                    Positioned(
                      top: 7.h,
                      left: 0,
                      child: SvgPicture.asset(
                        AppIcon.kedus2,
                      ),
                    ),
                    Positioned(
                      top: 0,
                      left: 0,
                      child: SvgPicture.asset(
                        AppIcon.kedus3,
                      ),
                    ),
                    Positioned(
                      top: 0,
                      left: 42.w,
                      child: Image.asset(
                        AppIcon.kedus4,
                      ),
                    ),
                    Positioned(
                      top: 0,
                      left: 63.w,
                      child: Image.asset(
                        AppIcon.kedus5,
                      ),
                    ),
                  ],
                ),
              ),
              // النصوص مع PoppinsText
              SizedBox(height: 16.w),
              const PoppinsText(
                text: AppString.banner1,
                fontSize: 16,
                color: AppColor.kBgColor,
                fontWeight: FontWeight.w700,
              ),
              SizedBox(height: 4.w),
              const PoppinsText(
                text: AppString.banner2,
                fontSize: 13,
                color: AppColor.kBgColor,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
              ),
              SizedBox(height: 16.w),
              GestureDetector(
                onTap: () {
                  // تنفيذ عند الضغط
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5.r),
                    color: Colors.white,
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: const PoppinsText(
                    text: AppString.learnMore,
                    fontSize: 13,
                    color: AppColor.kPrimaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(height: 16.w),
            ],
          ),
        ),
      ),
    );
  }
}
