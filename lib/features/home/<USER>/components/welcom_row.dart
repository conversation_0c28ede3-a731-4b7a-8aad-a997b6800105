import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class WelcomeRow extends StatelessWidget {
  final Widget poppinsText;
  final void Function() onTap;
  const WelcomeRow({super.key, required this.poppinsText, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        poppinsText,
        const Spacer(),
        InkWell(
            onTap: onTap,
            child: SizedBox(
                width: 24.w,
                height: 24.w,
                child: SvgPicture.asset(AppIcon.kBellIcon))),
      ],
    );
  }
}
