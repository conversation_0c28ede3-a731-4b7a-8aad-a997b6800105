import 'dart:math';

import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UpcomingList extends StatefulWidget {
  const UpcomingList({super.key});

  @override
  State<UpcomingList> createState() => _UpcomingListState();
}

class _UpcomingListState extends State<UpcomingList> {
  final List<Color> colors = [
     AppColor.khomeListColor1.withOpacity(0.5),
    AppColor.khomeListColor2.withOpacity(0.5),
    AppColor.khomeListColor3.withOpacity(0.5),
    AppColor.khomeListColor4.withOpacity(0.5),
    AppColor.khomeListColor5.withOpacity(0.5),
  ];

  final Random random = Random();

  late List<Color> itemColors;

  @override
  void initState() {
    super.initState();
    // Generate a list of colors once and store it
    itemColors =
        List.generate(50, (index) => colors[random.nextInt(colors.length)]);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 125.h,
      child: ListView.builder(
          padding: EdgeInsets.only(left: 16.w),
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          itemCount: 10,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: EdgeInsets.only(right: 8.w),
              child: Container(
                width: 174.w,
                height: 124.h,
                decoration: BoxDecoration(
                    color: itemColors[index],
                    borderRadius: BorderRadius.circular(10.r)),
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const PoppinsText(
                        text: "Mathematics",
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      Row(
                        children: [
                          const PoppinsText(
                            text: "11:30",
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          SizedBox(
                            width: 4.w,
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 4.h),
                            child: const PoppinsText(
                              text: "13:30",
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Color(0xff576B74),
                            ),
                          ),

                        ],
                      ),SizedBox(
                        height: 8.h,
                      ),
                      Row(
                        children: [
                          Container(
                            width: 24.w,
                            height: 24.h,
                            decoration: BoxDecoration(
                                color: Colors.greenAccent,
                                borderRadius: BorderRadius.circular(100.r)
                            ),
                          ),
                          SizedBox(width: 4.w,),
                          const PoppinsText(
                            text: "Waleed Soliman",
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: Color(0xff576B74),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            );
          }),
    );
  }
}
