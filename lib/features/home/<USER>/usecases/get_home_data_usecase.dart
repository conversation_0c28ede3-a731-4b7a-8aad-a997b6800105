import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecase/base_usecase.dart';
import '../entities/home_data.dart';
import '../repository/home_repository.dart';

class GetHomeDataUseCase implements UseCase<HomeData, String> {
  final HomeRepository repository;

  GetHomeDataUseCase(this.repository);

  @override
  Future<Either<Failure, HomeData>> call(String language) async {
    return await repository.getHomeData(language);
  }
}
