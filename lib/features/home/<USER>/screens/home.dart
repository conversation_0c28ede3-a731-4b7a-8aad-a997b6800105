import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/home/<USER>/components/promo_bannel_Widget.dart';
import 'package:edus_student_ms/features/home/<USER>/components/view_all_text.dart';
import 'package:edus_student_ms/features/home/<USER>/components/welcom_row.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/utils/app_font.dart';
import '../components/home_work_list.dart';
import '../components/upcoming_list.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: WelcomeRow(
            poppinsText: const PoppinsText(
                text: "Welcome, Waleed",
                fontSize: 24,
                fontWeight: FontWeight.w600),
            onTap: () {
              // Navigator.pushNamed(context, kNotifications);
            }),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 16.h,
            ),
            const ViewAllText(
                text1: AppString.upcomingClass, text2: AppString.viewAll),
            SizedBox(
              height: 12.h,
            ),
            const UpcomingList(),
            SizedBox(
              height: 16.h,
            ),
            const EduspromotbannelWidget(),
            SizedBox(
              height: 14.h,
            ),
            const ViewAllText(
                text1: AppString.homework, text2: AppString.viewAll),
            SizedBox(
              height: 12.h,
            ),
            const HomeWorkList()
          ],
        )),
      ),
    );
  }
}
