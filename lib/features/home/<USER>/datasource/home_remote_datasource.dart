import '../../../../core/network/api_constant.dart';
import 'package:dio/dio.dart';

final Dio dio = Dio(BaseOptions(
  baseUrl: ApiConstants.baseUrl,
  connectTimeout: const Duration(seconds: 3000),
  receiveTimeout: const Duration(seconds: 3000),
  sendTimeout: const Duration(seconds: 3000),
  validateStatus: (status) {
    return status! < 501;
  },
  headers: {
    'Content-Type': 'application/json',
  },
));

abstract class HomeRemoteDataSource {
  // Future<LoginModel> doLogin(LoginParams params);
 
}

class HomeRemoteDataSourceImpl implements HomeRemoteDataSource {
  HomeRemoteDataSourceImpl();

  // @override
  // Future<LoginModel> doLogin(LoginParams params) async {
  //   final response = await dio.post(ApiConstants.loginUrl,
  //       data: FormData.fromMap(params.toJson()),
  //       options: Options(headers: {"Content-Type": "multipart/form-data"}));
  //   log("${response.data}-----password-${params.password}  -email-${params.email} ");

  //   return switch (response.statusCode!) {
  //     >= 200 && < 300 =>
  //       LoginModel.fromJson(const JsonDecoder().convert(response.data)),
  //     404 => throw ServerException<String>("Email not found"),
  //     >= 400 && < 500 => throw ServerException<String>(response.data),
  //     _ => throw ServerException("${response.data}")
  //   };
  // }

}
