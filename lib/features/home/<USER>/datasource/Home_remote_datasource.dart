import 'package:edus_student_ms/core/services/service_locator.dart';
import 'package:edus_student_ms/core/services/token_service/token_storage.dart';

import '../../../../core/network/api_constant.dart';
import '../../../../core/error/exceptions.dart';
import '../models/home_data_model.dart';
import 'package:dio/dio.dart';
import 'dart:convert';
import 'dart:developer';

final Dio dio = Dio(BaseOptions(
  baseUrl: ApiConstants.baseUrl,
  connectTimeout: const Duration(seconds: 3000),
  receiveTimeout: const Duration(seconds: 3000),
  sendTimeout: const Duration(seconds: 3000),
  validateStatus: (status) {
    return status! < 501;
  },
  headers: {
    'Content-Type': 'application/json',
  },
));

abstract class HomeRemoteDataSource {
  Future<HomeDataModel> getHomeData(String language);
}

class HomeRemoteDataSourceImpl implements HomeRemoteDataSource {
  HomeRemoteDataSourceImpl();

  @override
  Future<HomeDataModel> getHomeData(String language) async {
    final token = await sl<TokenStorage>().getToken();
    final response = await dio.get(
      ApiConstants.homeUrl,
      queryParameters: {'language': language},
      options: Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      ),
    );

    log("Home API Response: ${response.data}");

    return switch (response.statusCode!) {
      >= 200 && < 300 =>
        HomeDataModel.fromJson(const JsonDecoder().convert(response.data)),
      404 => throw ServerException<String>("Home data not found"),
      >= 400 && < 500 => throw ServerException<String>(response.data),
      _ => throw ServerException("${response.data}")
    };
  }
}
