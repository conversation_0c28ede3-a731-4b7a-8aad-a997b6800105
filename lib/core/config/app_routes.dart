import 'package:edus_student_ms/features/profile/presentation/screens/login_security.dart';
import 'package:edus_student_ms/features/profile/presentation/screens/notification.dart';
import 'package:edus_student_ms/features/profile/presentation/screens/personal_informatio.dart';
import 'package:flutter/material.dart';

import '../../features/auth/presentation/screens/auth/login.dart';
import '../../features/auth/presentation/screens/auth/signup.dart';
import '../../features/auth/presentation/screens/auth/signup_with_email.dart';
import '../utils/navigation.dart';

class AppRoutes {
  static Route onGenerateRoutes(RouteSettings settings) {
    switch (settings.name) {
      case '/':
        return _materialRoute(const BottomNavigation(), settings);

      case kBottomNavigation:
        return _materialRoute(const BottomNavigation(), settings);
      case kLogin:
        return _materialRoute(const Login(), settings);
      case kSignup:
        return _materialRoute(const Signup(), settings);
      case kSignupWithEmail:
        return _materialRoute(const SignupWithEmail(), settings);
      case kPersonalInformation:
        return _materialRoute(const PersonalInformation(), settings);
      case kLoginScurity:
        return _materialRoute(const LoginAndSecurity(), settings);
      case knotification:
        return _materialRoute(const NotificationScreen(), settings);

      default:
        return _materialRoute(const Text("Hobba"), settings);
    }
  }

  static Route<dynamic> _materialRoute(Widget view, RouteSettings settings) {
    return MaterialPageRoute(builder: (_) => view, settings: settings);
  }
}

const String kBottomNavigation = "/bottomNavigation";
const String kLogin = "/login";
const String kSignup = "/signup";
const String kSignupWithEmail = "/signupWithEmail";
const String kPersonalInformation = "/personalInformation";
const String kLoginScurity = "/LoginScurity";
const String knotification = "/notification";
