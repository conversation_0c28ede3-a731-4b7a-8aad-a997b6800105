import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TitleMain extends StatelessWidget {
  final String title;
  final double? horizontal;
  const TitleMain({super.key, required this.title, this.horizontal});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.symmetric(vertical: 0.h, horizontal: horizontal ?? 0.0),
      child: PoppinsText(
        text: title,
        fontSize: 24,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}
