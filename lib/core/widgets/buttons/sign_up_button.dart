import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/app_color.dart';
import '../../utils/app_font.dart';

class SignUpButton extends StatelessWidget {
  final VoidCallback onPress;
  final bool? isButtonDisabled;
  final Color? buttonColor;
  final Color? textColor;

  // ignore: use_key_in_widget_constructors
  const SignUpButton({
    required this.onPress,
    this.isButtonDisabled,
    this.buttonColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: 48.h,
        width: 343.w,
        child: ElevatedButton(
            onPressed: isButtonDisabled == null ? onPress : null,
            style: ButtonStyle(
                elevation: MaterialStateProperty.all(0),
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                        side: const BorderSide(color: AppColor.kPrimaryColor))),
                backgroundColor: MaterialStateProperty.all(
                    isButtonDisabled == null
                        ? buttonColor ?? AppColor.kPrimaryColor
                        : const Color(0xffFAFAFA))),
            child: Center(
                child: PoppinsText(
                    text: AppString.signUp,
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: isButtonDisabled == null
                        ? textColor ?? Colors.white
                        : const Color(0xffBA9FC3)))));
  }
}
