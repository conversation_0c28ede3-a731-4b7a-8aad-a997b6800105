import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:form_validator/form_validator.dart';

import '../../utils/app_icon.dart';

class SearchInput extends StatelessWidget {

  final String? label;
  final String? hint;
  final TextInputType? inputType;
  final TextEditingController controller;
  final List<TextInputFormatter>? inputFormatters;

  const SearchInput(
      {Key? key,
        this.label,
        this.inputType,
        required this.controller,
        this.hint,
        this.inputFormatters})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      cursorColor: const Color.fromRGBO(156, 162, 170, 1.0),
      keyboardType: inputType,
      validator: ValidationBuilder().required("Field Required").build(),
      inputFormatters: inputFormatters,
      controller: controller,
      style: AppFont.kFontPoppinsStyle.copyWith(
          color: const Color.fromRGBO(156, 162, 170, 1.0),
          fontWeight: FontWeight.w400,
          fontSize: 13.sp),
      decoration: InputDecoration(

        prefixIconConstraints: BoxConstraints(
          maxHeight: 44.w,
          maxWidth: 44.w,
          minHeight: 44.w,
          minWidth: 44.w,
        ),
        prefixIcon: SizedBox(
          width: 44.w,height: 44.w,
            child: Center(child: SvgPicture.asset(AppIcon.kSearchIcon,width: 24.w,height: 24.w,))),
        labelStyle: AppFont.kFontPoppinsStyle.copyWith(
            color: const Color.fromRGBO(196, 196, 196, 1),
            fontWeight: FontWeight.w400,
            fontSize: 13.sp),
        hintText: label,
        hintStyle: AppFont.kFontPoppinsStyle.copyWith(
            color: const Color.fromRGBO(156, 162, 170, 1.0),
            fontWeight: FontWeight.w400,
            fontSize: 13.sp),
        contentPadding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
        border: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColor.kBgColor),
          borderRadius: BorderRadius.circular(10.r),
        ),
        disabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColor.kBgColor),
          borderRadius: BorderRadius.circular(10.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColor.kBgColor),
          borderRadius: BorderRadius.circular(10.r),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColor.kBgColor),
          borderRadius: BorderRadius.circular(10.r),
        ),
        filled: true,
        fillColor: Color(0xffECEEF3),
      ),
    );
  }
}
