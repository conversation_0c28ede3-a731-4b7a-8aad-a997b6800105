import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../utils/app_font.dart';

class TextInput extends StatefulWidget {
  final String icon;
  final String? label;
  final String hint;
  final FormFieldValidator<String> validator;
  final TextInputType inputType;
  final TextEditingController controller;
  final List<TextInputFormatter>? inputFormatters;

  const TextInput(
      {Key? key,
      this.label,
      required this.validator,
      required this.icon,
      required this.inputType,
      required this.controller,
      required this.hint,
      this.inputFormatters})
      : super(key: key);

  @override
  State<TextInput> createState() => _TextInputState();
}

class _TextInputState extends State<TextInput> {
  bool iconColor = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: TextFormField(
        onTap: () {
          setState(() {
            iconColor = true;
          });
        },
        onFieldSubmitted: (text) {
          setState(() {
            text == "" ? iconColor = false : iconColor = true;
          });
        },
        onTapOutside: (text) {
          setState(() {
            widget.controller.text == "" ? iconColor = false : iconColor = true;
          });
        },
        cursorColor: AppColor.kPrimaryColor,
        keyboardType: widget.inputType,
        validator: widget.validator,
        inputFormatters: widget.inputFormatters,
        controller: widget.controller,
        style: AppFont.kFontPoppinsStyle.copyWith(
            color: Colors.black, fontWeight: FontWeight.w500, fontSize: 15.sp),
        decoration: InputDecoration(
            // label: Text(widget.label),
            labelStyle: AppFont.kFontPoppinsStyle.copyWith(
                color: AppColor.kSecondaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 12.sp),
            floatingLabelStyle: AppFont.kFontPoppinsStyle.copyWith(
                color: AppColor.kPrimaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 15.sp),
            hintText: widget.hint,
            hintStyle: AppFont.kFontPoppinsStyle.copyWith(
                color: AppColor.kSecondaryColor,
                fontWeight: FontWeight.w400,
                fontSize: 16.sp),
            contentPadding: EdgeInsets.symmetric(vertical: 10.h),
            prefixIconConstraints: BoxConstraints(
              maxHeight: 54.h,
              maxWidth: 64.w,
              minHeight: 54.h,
              minWidth: 64.w,
            ),
            prefixIcon: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
              child: SvgPicture.asset(
                widget.icon,
                height: 24.h,
                width: 24.w,
                fit: BoxFit.fitHeight,
                color: iconColor == true
                    ? AppColor.kPrimaryColor
                    : AppColor.kSecondaryColor,
              ),
            ),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColor.kPrimaryColor),
              borderRadius: BorderRadius.circular(10.r),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColor.kPrimaryColor),
              borderRadius: BorderRadius.circular(10.r),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColor.kPrimaryColor),
              borderRadius: BorderRadius.circular(10.r),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColor.kSecondaryColor),
              borderRadius: BorderRadius.circular(10.r),
            ),
            filled: true,
            fillColor: AppColor.kBgColor),
      ),
    );
  }
}
