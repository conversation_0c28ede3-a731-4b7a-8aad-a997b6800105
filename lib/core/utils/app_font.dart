
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_color.dart';

class AppFont{
  static TextStyle kFontPoppinsStyle =GoogleFonts.poppins(
    color:  AppColor.kBlackColor ,
    fontWeight:  FontWeight.w500,
    fontSize: 14.sp ,
    decoration: TextDecoration.none,
  );

}
class PoppinsText extends StatelessWidget {
  final String  text;
  final Color? color;
  final FontWeight ?fontWeight;
  final int fontSize;
  final TextAlign ?textAlign;
  final TextOverflow ? textOverflow;
  final int ? maxLines;
  final TextDecoration ? textDecoration;

  const PoppinsText(

      {super.key, required this.text,
        this.color,
        this.fontWeight ,
        this.textOverflow ,
        this.maxLines ,
        this.textDecoration ,
        required this.fontSize,
        this.textAlign});
  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      overflow:textOverflow,
      maxLines: maxLines,
      softWrap: true,
      textAlign: textAlign ?? TextAlign.center,
      style: GoogleFonts.poppins(
        color: color ??  Colors.black ,
        fontWeight: fontWeight ?? FontWeight.w500,
        fontSize: fontSize.sp ,
        decoration: textDecoration ??TextDecoration.none,
      ),
    );
  }}