import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/attendance/presentation/screens/attendance.dart';
import 'package:edus_student_ms/features/classes/presentation/screens/classes.dart';
import 'package:edus_student_ms/features/fees/presentation/screens/fees.dart';
import 'package:edus_student_ms/features/home/<USER>/screens/home.dart';
import 'package:edus_student_ms/features/profile/presentation/screens/profile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';



class BottomNavigation extends StatefulWidget {
  static final GlobalKey globalKey = GlobalKey(debugLabel: 'btm_app_bar');

  const BottomNavigation({Key? key}) : super(key: key);

  @override
  _BottomNavigationState createState() => _BottomNavigationState();
}

class _BottomNavigationState extends State<BottomNavigation> {
  int _selectedIndex = 0;

  static final List<Widget> _bottomNavItems = [
    const Home(),
    const Classes(),
    const Attendance(),
    const Fees(),
    const Profile(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: <Widget>[
          _bottomNavItems.elementAt(_selectedIndex),
          Align(
              alignment: Alignment.bottomCenter,
              child: Theme(
                  data: Theme.of(context)
                      .copyWith(canvasColor: Colors.transparent),
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.r),
                        topRight: Radius.circular(12.r)),
                    child: BottomNavigationBar(
                      enableFeedback: false,
                      key: BottomNavigation.globalKey,
                      items: <BottomNavigationBarItem>[
                        BottomNavigationBarItem(
                            icon: Padding(
                              padding: EdgeInsets.only(bottom: 6.h,top: 4.h),
                              child: SvgPicture.asset(
                                AppIcon.kHomeIcon,
                                height: 24.h,
                                width: 24.w,
                                color: _selectedIndex == 0
                                    ? AppColor.kPrimaryColor
                                    : const Color(0xff9CA2AA),
                              ),
                            ),
                            label: AppString.home),
                        BottomNavigationBarItem(
                            icon: Padding(
                              padding: EdgeInsets.only(bottom: 6.h,top: 4.h),
                              child: SvgPicture.asset(
                                AppIcon.kClassesIcon,
                                height: 24.h,
                                width: 24.w,
                                color: _selectedIndex == 1
                                    ? AppColor.kPrimaryColor
                                    : const Color(0xff9CA2AA),
                              ),
                            ),
                            label: AppString.classes),BottomNavigationBarItem(
                            icon: Padding(
                              padding: EdgeInsets.only(bottom: 6.h,top: 4.h),
                              child: SvgPicture.asset(
                                AppIcon.kAttendIcon,
                                height: 24.h,
                                width: 24.w,
                                color: _selectedIndex == 2
                                    ? AppColor.kPrimaryColor
                                    : const Color(0xff9CA2AA),
                              ),
                            ),
                            label: AppString.attend),
                        BottomNavigationBarItem(
                          icon: Padding(
                            padding: EdgeInsets.only(bottom: 6.h,top: 4.h),
                            child: SvgPicture.asset(
                              AppIcon.kFeesIcon,
                              height: 24.h,
                              width: 24.w,
                              color: _selectedIndex == 3
                                  ? AppColor.kPrimaryColor
                                  : const Color(0xff9CA2AA),
                            ),
                          ),
                          label: AppString.fees,
                        ),

                        BottomNavigationBarItem(
                            icon: Padding(
                              padding: EdgeInsets.only(bottom: 6.h,top: 4.h),
                              child: SvgPicture.asset(
                                AppIcon.kUserIcon,
                                height: 24.h,
                                width: 24.w,
                                color: _selectedIndex == 4
                                    ? AppColor.kPrimaryColor
                                    : const Color(0xff9CA2AA),
                              ),
                            ),
                            label: AppString.profile),
                      ],
                      currentIndex: _selectedIndex,
                      backgroundColor:
                          const Color(0xfff9f9f9).withAlpha(84).withOpacity(0.8),
                      fixedColor: const Color(0xff5B0579),
                      unselectedLabelStyle: GoogleFonts.poppins(
                        color: const Color(0xff6B7280),
                        fontWeight: FontWeight.w500,
                        fontSize: 10.sp,
                      ),
                      selectedLabelStyle: GoogleFonts.poppins(
                        color: const Color(0xff5B0579),
                        fontWeight: FontWeight.w700,
                        fontSize: 10.sp,
                      ),
                      type: BottomNavigationBarType.fixed,
                      showUnselectedLabels: true,
                      showSelectedLabels: true,
                      elevation: 0,
                      selectedFontSize: 0.sp,
                      onTap: _onItemTapped,
                    ),
                  ))),
        ],
      ),
    );
  }
}
