import 'package:flutter/material.dart';

class AppColor {
  static const Color kPrimaryColor = Color(0xff9708C9);
  static const Color kSecondaryColor = Color(0xffD9D9D9);
  static const Color kBgColor = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color kBlackColor = Color(0xff000000);
  static const Color kGrayColor = Color(0xff94A5AB);
  static const Color kDarkGrayColor = Color(0xff333333);
  static const Color kDarkBlueColor = Color(0xff1D2B4F);
  static const Color kRedColor = Color(0xffEE1D52);
  static const Color kIconColor = Color(0xff131117);
  static const Color kBorderColor = Color(0xffD3D3D3);
  static const Color kInputPrimaryColor = Color(0xff188d5e);
  static const Color kSecondChartGradientColor = Color(0xff00D083);
  static const Color khomeListColor1 = Color(0xff9195F6);
  static const Color khomeListColor2 = Color(0xffB7C9F2);
  static const Color khomeListColor3 = Color(0xffF9F07A);
  static const Color khomeListColor4 = Color(0xffFB88B4);
  static const Color khomeListColor5 = Color(0xff94FFD8);
  static const Color khomeListColor6 = Color(0xff576B74);
  static const Color kgreen = Color(0xff32D74B);
  static const Color kclassesLineListColor = Color(0xFFD599E9);
  static const Color kclassesAttendedColor = Color.fromRGBO(52, 199, 89, 1);
  static const Color kattendedCalendarBox = Color.fromARGB(255, 224, 228, 230);
  static const Color kfessbordercolor = Color.fromRGBO(184, 196, 201, 1);
  static const Color kfeesPaid = Color.fromRGBO(52, 199, 89, 1);
  static const Color klogout = Color.fromRGBO(255, 59, 48, 1);
}
