
import 'package:edus_student_ms/features/auth/domain/usecases/get_signup_data_usecase.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';

import '../../features/auth/data/datasource/auth_remote_datasource.dart';
import '../../features/auth/data/repository/auth_repository_impl.dart';
import '../../features/auth/domain/repository/auth_repository.dart';
import '../../features/auth/domain/usecases/get_login_data_usecase.dart';
import '../../features/auth/domain/usecases/reset_password_use_case.dart';
import '../../features/auth/presentation/controller/auth_bloc.dart';

import '../../features/home/<USER>/datasource/home_remote_datasource.dart';
import '../../features/home/<USER>/repository/home_repository_impl.dart';
import '../../features/home/<USER>/repository/home_repository.dart';
import '../../features/home/<USER>/usecases/get_home_data_usecase.dart';
import '../../features/home/<USER>/controller/home_bloc.dart';

import 'storage_service/storage_impl.dart';
import 'token_service/token_secure_storage.dart';
import 'token_service/token_storage.dart';

final  sl = GetIt.instance;

//
class ServiceLocator {
  void init() {
//     /// Bloc
    sl.registerLazySingleton(() => AuthBloc(sl(), sl(), sl()));
    sl.registerLazySingleton(() => HomeBloc(sl()));


    // Use Cases
    sl.registerLazySingleton<DoLoginUseCase>(() => DoLoginUseCase(sl()));
    sl.registerLazySingleton<ResetPasswordUseCase>(() => ResetPasswordUseCase(sl()));
    sl.registerLazySingleton<DoSignUpUseCase>(() => DoSignUpUseCase(sl()));
    sl.registerLazySingleton<GetHomeDataUseCase>(() => GetHomeDataUseCase(sl()));


    // Repository
    sl.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(sl()));
    sl.registerLazySingleton<HomeRepository>(() => HomeRepositoryImpl(sl()));


    // DATA SOURCE
    sl.registerLazySingleton<AuthRemoteDataSource>(() => AuthRemoteDataSourceImpl());
    sl.registerLazySingleton<HomeRemoteDataSource>(() => HomeRemoteDataSourceImpl());


    // Storage Service
    sl.registerLazySingleton<FlutterSecureStorage>(() => const FlutterSecureStorage());
    sl.registerLazySingleton<TokenStorage>(() => TokenFlutterSecureStorageService(sl()));


    // LANG

  }
}
