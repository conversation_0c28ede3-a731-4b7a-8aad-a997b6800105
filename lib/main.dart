import 'package:edus_student_ms/features/auth/presentation/screens/auth/login.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'core/config/app_routes.dart';
import 'core/services/service_locator.dart';
import 'core/utils/navigation.dart';
import 'features/auth/presentation/controller/auth_bloc.dart';

void main() async {
  ServiceLocator().init();
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(
    MultiBlocProvider(providers: [
      BlocProvider<AuthBloc>(
        create: (context) => sl<AuthBloc>(),
      ),
    ], child: const MyApp()),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        minTextAdapt: true,
        designSize: const Size(375, 812),
        builder: (context, child) => MaterialApp(
              debugShowCheckedModeBanner: false,
              theme: ThemeData(
                primarySwatch: Colors.deepPurple,
              ),
              builder: (context, widget) {
                return MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                      textScaler:
                          TextScaler.linear(1.0)), //old >> textScaleFactor: 1.0
                  child: widget!,
                );
              },
              home: const Login(),
              // home: const OnBoarding(),
              onGenerateRoute: AppRoutes.onGenerateRoutes,
            ));
  }
}
