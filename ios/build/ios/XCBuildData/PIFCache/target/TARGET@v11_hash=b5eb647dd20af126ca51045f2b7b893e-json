{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ea4f537f547c488a2fd5cf1d9bf199c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e173b786eac7349b43091970c138432", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b32a9c019dd01f06aee62e5d6323e72", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9804055e617661efe344647dce882a1056", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b32a9c019dd01f06aee62e5d6323e72", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9805e6eb6886abba4cf0f644b21693dcfb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98954751d551571ed2d6b7da7db15dd091", "guid": "bfdfe7dc352907fc980b868725387e985b096683f6b7926dd74daa315ce9d7d7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e64e43e28580f6399dce2be0a6fd05c9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98df0f3d39b10e4e8083e2161db1a80063", "guid": "bfdfe7dc352907fc980b868725387e98fdb0bf09f80ac968968bc1d91fc30464"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b1e3401e2d0e787a4de44b410c4855", "guid": "bfdfe7dc352907fc980b868725387e981e410dbc6ece2b178b543251e45aed0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873f39c6a70bf1b4af5b4b3cb70472b00", "guid": "bfdfe7dc352907fc980b868725387e984f3d8bb8567ed7a680564bdf77128d96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852524f5b40fae77d69852de5af805201", "guid": "bfdfe7dc352907fc980b868725387e98065041f408c8e24b9ff02b954873fb39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5b0b06147c72f08d3fcae3538f93aa6", "guid": "bfdfe7dc352907fc980b868725387e980facc9d4fe5a422f053f361ff83de689"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982158076b336997983628edce1b52617c", "guid": "bfdfe7dc352907fc980b868725387e981580ec4e0b1b707dbdaba1f7f4154410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98436692072d7b818faafcc405d64ba3bb", "guid": "bfdfe7dc352907fc980b868725387e98dd7c4c14013b5d69b0228a93b25d3931"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4299764c5c4631bbb1fa7e0aba1ef15", "guid": "bfdfe7dc352907fc980b868725387e982a25e9a8529035b9da3badff03eb6dbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b119917f86e25d6fcd32f128fbc2498", "guid": "bfdfe7dc352907fc980b868725387e98e788bbb1681f5a05132e86f255952ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1dcf16b36b97bb19fa219cfb630c61f", "guid": "bfdfe7dc352907fc980b868725387e987cd3c95a9d28b5836b036156084b59a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd46e28df18557b70042ce85a5ade289", "guid": "bfdfe7dc352907fc980b868725387e984ef14fdfd7d860c3c69dc26e8a27fe4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a48c0327a1340f005abca3b8edb71e", "guid": "bfdfe7dc352907fc980b868725387e98052c5b610483a990bec8efe80bae4944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeca7353a983eac38328bc2ffe646e13", "guid": "bfdfe7dc352907fc980b868725387e9871d1e08b5de1b8ef4fa2635590193653"}], "guid": "bfdfe7dc352907fc980b868725387e9886705e38c5caed62469f5e31b40d10f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d6e5dc93de40640996855ac1715a298", "guid": "bfdfe7dc352907fc980b868725387e98ffe03c22c15323bb1aa6b25faf0ade46"}], "guid": "bfdfe7dc352907fc980b868725387e98916ab872285a51de8c953c14cb52b96a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cdb0d93d7446026628172370bc8d83ff", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98b0a842aa431aa11be567e48be2fc317d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}