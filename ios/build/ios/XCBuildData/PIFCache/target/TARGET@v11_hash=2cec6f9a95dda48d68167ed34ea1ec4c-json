{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986572f88554bf292103424423394caa73", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806aedfc8b56b721c2991a4cb86a2ad22", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824146c006e3cf136c57d71b7e766fb76", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983f87b9962fbaca590b3c2022c5b864c1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824146c006e3cf136c57d71b7e766fb76", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98492ec51a3f097b7dc05c23a1858695f4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ddffc3d812b2f35a05a8a202e7c60ab7", "guid": "bfdfe7dc352907fc980b868725387e9834ad90330c33f7c28bf4e846b53df7d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98828d3a6ed9c9ffed11ef958d2c83a6af", "guid": "bfdfe7dc352907fc980b868725387e98d06c9fe20097359734313079a75ce422", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae9fdbca436450d6cc0fb61c6e24791", "guid": "bfdfe7dc352907fc980b868725387e98df5785587f9b2f66adf4d348aa62848d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488b9ccf9aa30adc9d08d083296056fc", "guid": "bfdfe7dc352907fc980b868725387e98d6c9e6b05a062209dea3b09e5e0f413f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6edd98afa778736dfb53150ab59e2fe", "guid": "bfdfe7dc352907fc980b868725387e982560534265a27c9e9080bd8834ec9876", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989be702d52805c4ea8bacb3811925d739", "guid": "bfdfe7dc352907fc980b868725387e9845e7fabd41adebc851b6425db8aeaee4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982630896806e02487cf9a12a1cce3d0ed", "guid": "bfdfe7dc352907fc980b868725387e980f48b1f43230b42e2d600c84c87f86a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5aea0c9bff6eed24af42f3c5721713", "guid": "bfdfe7dc352907fc980b868725387e9819d2a464520ef6e9f0ee0701138feddb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983589d57c4b71798f0de3680cfd3f0a48", "guid": "bfdfe7dc352907fc980b868725387e98a8b8356a408e658808fc818c1234d2b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf84349d832efa560fe660d33c9a7493", "guid": "bfdfe7dc352907fc980b868725387e989f58f3b4e1e8ba4c8f397b23c58bb5c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d141e64a91060d07600be95b409e4943", "guid": "bfdfe7dc352907fc980b868725387e9853a5c5ab78ff02be8cc1a8a0e195b8b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e6fa4e4792c86884066b7d50bd4acb4", "guid": "bfdfe7dc352907fc980b868725387e980a3ae74c56c9874c72688451f8eb6640", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aa4a78073a1334029e6b32d1d9d4974", "guid": "bfdfe7dc352907fc980b868725387e98ea50557afc8b1cb7e21e24505dedea14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b324d2e086f572149459694c331ad383", "guid": "bfdfe7dc352907fc980b868725387e98914c864994450ca9249d9c00eb2d5369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a122aa61dd86fabc9068b31cc555196", "guid": "bfdfe7dc352907fc980b868725387e98b3cc33dc90e41de450bf655b1d26814f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c69bf317ea5a24fab34b51f03ca0f785", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b38d313622e32df31b42b5f2c12f82dc", "guid": "bfdfe7dc352907fc980b868725387e982821adf2b1592f8b8b5e88d290912364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580817dce03fb780666a072412c79a44", "guid": "bfdfe7dc352907fc980b868725387e98518ae48be20b5e582cec6538968782df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4a6ea3f1a720a727be5b93d767a636", "guid": "bfdfe7dc352907fc980b868725387e98e6264b045edf0f7f8cfa7e089bdc5073"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4cae2b4ef78450de028c1340ec493c", "guid": "bfdfe7dc352907fc980b868725387e9804cc4dd4e305f2b8407fdeade2f2a84c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810124b90a7425f62b2aef0e30b81adff", "guid": "bfdfe7dc352907fc980b868725387e983a83ce55fcc8ef17cb2537f1b10a8062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a2ff4220bb4d967a24ae44ff267998", "guid": "bfdfe7dc352907fc980b868725387e98679e357143c9be175e92e7d42bcf6973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872f2f8b7dbfa56117d677f410c925773", "guid": "bfdfe7dc352907fc980b868725387e98a2b442dc0b84a3a6575da2c7a40ff5c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2d58273db2fcde1404352661e6ca73", "guid": "bfdfe7dc352907fc980b868725387e98d3ef6e34b764c8da95d11b1d42a28ff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ef2553f919bc5f7067ff3bc3d573f0", "guid": "bfdfe7dc352907fc980b868725387e9820f89016c3673019369e289f6bdd5ffb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c40b58f6ce157f22c00e01f6919fd8", "guid": "bfdfe7dc352907fc980b868725387e98dc706b8a266a64c0c3439b0bced6b87c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f3a0fbe28457823ef04cb03d99887f", "guid": "bfdfe7dc352907fc980b868725387e983e35048ed28c4baf8691ed981aee8b04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd45b28ed75d787fa31aafee41aa14dc", "guid": "bfdfe7dc352907fc980b868725387e9863690ef239e183b8789c906dc6ba5489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a167f24a98e06b4c01266e216f172af8", "guid": "bfdfe7dc352907fc980b868725387e9864f4f669a9f3354fa50efa6001d4ea3d"}], "guid": "bfdfe7dc352907fc980b868725387e98b7b748ec2840e8a6198298020255d75d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d6e5dc93de40640996855ac1715a298", "guid": "bfdfe7dc352907fc980b868725387e98cae5639098659dcb5691a7bd48b74147"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa4ed02bd5be8df8bb54b59f47e8804", "guid": "bfdfe7dc352907fc980b868725387e984d3c958f97cd8264c58000aa13aeae77"}], "guid": "bfdfe7dc352907fc980b868725387e98dafade3e7d25ede641a5b4b3efa8d689", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e63e1c5da2af37471c6b9c4e2470dbe3", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e989b23a4b9eb770959f3b9651bbd9c3557", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}