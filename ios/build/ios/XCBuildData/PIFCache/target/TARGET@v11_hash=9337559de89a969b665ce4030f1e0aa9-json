{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f781e1180babf611e7210fb884529241", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9841eb0d65445630f0ff27fa512ef48289", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d07ec9abc00fcb5f07f41dae919e49ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879b625c80c060e7f6761ca28f6fc7d97", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d07ec9abc00fcb5f07f41dae919e49ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9828326f966ed527a1c1b7b46d7bbb1ab4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9859ff1070308ab3892b9277c8e36f2cba", "guid": "bfdfe7dc352907fc980b868725387e98c6b8b1cc9508487fdb4a2cc611ea87a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98637b138a0e019e670f82292defd482bb", "guid": "bfdfe7dc352907fc980b868725387e98ab5703efc8a4dcff07ad7d61b722ee0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892c9ba48492c3dc7e60c9b577579f5e4", "guid": "bfdfe7dc352907fc980b868725387e98b32114e008e246919ad75ee62131ba5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98576f4aea80aadec1c2e0a8defd0f1eb5", "guid": "bfdfe7dc352907fc980b868725387e982fde87d8396011f667ebada488fa60ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823124d0463b30141d5d2c7ccbcf9f899", "guid": "bfdfe7dc352907fc980b868725387e9868b1932aefcd4a336f7cd773590fe4e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe36f32ef7e15492258632c5526b0acb", "guid": "bfdfe7dc352907fc980b868725387e98f0058c388c126c34ce06e8b7867a97ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98466d3a72a4e47601a93d4f638c3d28a2", "guid": "bfdfe7dc352907fc980b868725387e9846ff376356b2397e76112b811dfb73ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c89e46b61b220db3b9464020d12f3ea6", "guid": "bfdfe7dc352907fc980b868725387e98d6dd095e64d78654dd63ceecc72bee8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893d3555e0e8fdd7fb56dde5bd8e31bec", "guid": "bfdfe7dc352907fc980b868725387e981a630d155e5aefa41e6768b8476181e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825bfe384fa3353d0f94eb1e206c93e0f", "guid": "bfdfe7dc352907fc980b868725387e98a85811e13fc689f437dbb7e24b169eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a8e0640f68034b268a00166fcd4e8c", "guid": "bfdfe7dc352907fc980b868725387e98ab6bc5694911ab62e0b288030bbe44b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848df47907bad685fff4197ecfe7f33b7", "guid": "bfdfe7dc352907fc980b868725387e98bb4500b9856378abdc9310bdadea5749", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f96bfff2562263c1af1974cf001d1c08", "guid": "bfdfe7dc352907fc980b868725387e98c8f54105a0080588f21e91466a1550a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5caf5f3328d2e4bec00dbfb0f2d985", "guid": "bfdfe7dc352907fc980b868725387e9843f1b6170830e224635933039a909ef5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b85f6a7a3ee59971f0c7e67d92e7495e", "guid": "bfdfe7dc352907fc980b868725387e9847e92e4cc932f6516a129efb1f34e8ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7606f3bafd2baba86eacb2c47631d4", "guid": "bfdfe7dc352907fc980b868725387e986ac37f1456d9acf060a245b24084d9f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48fbc0d3d107259fe51dba2eb61f2c3", "guid": "bfdfe7dc352907fc980b868725387e98df0bedcd45fed27cce6a3217ece7da9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875839696ea8680eb4529329b63767204", "guid": "bfdfe7dc352907fc980b868725387e98e09d764f586ae1db55d01f2f826b6331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad8dab128f4619027fe53cf55b6dd96c", "guid": "bfdfe7dc352907fc980b868725387e98bc665ac13ba7c89d33a2c131982c5af4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d190320a9d9504e36a20eb72eb2a4d", "guid": "bfdfe7dc352907fc980b868725387e981a63a272bdbdfe10eb35769a3f705328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c174c745910ae259144e73ee5859a31", "guid": "bfdfe7dc352907fc980b868725387e982f32e8b0be934fff6712d7fb80fb8f3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac150f30a208860466af761b3f8d3e2b", "guid": "bfdfe7dc352907fc980b868725387e983041ebc088868b904c810a29e3c6561f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9865f894bc17acabcf22831644f507fbae", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ad099e0fa998baea0bc71811418ce3d4", "guid": "bfdfe7dc352907fc980b868725387e98084e81dc9a0d413b88f358161c88555e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f808936ce258f940e13fdde3b300029", "guid": "bfdfe7dc352907fc980b868725387e98e0e8ff564617988a725483a41b621f93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e47d0ee2d564a5a16fb9324090b03f54", "guid": "bfdfe7dc352907fc980b868725387e983ba33de1963de756ddfd262d37ea3a26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987adb3673956b7274ee81fadee9a8b41a", "guid": "bfdfe7dc352907fc980b868725387e9819d4bde31f4388d6048e6cfc1f5f7c7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab1d143642d8b84e2d7c23608dd41f81", "guid": "bfdfe7dc352907fc980b868725387e98c200b6b6c82aa257acee9784ad5819aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555ecb9bce80f975bf7ff704130e2c9d", "guid": "bfdfe7dc352907fc980b868725387e98b3e7280cdc0025a6917d751fe3229f2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbf6b7a321d4363ed10a0406547b093a", "guid": "bfdfe7dc352907fc980b868725387e982c6ab50d3826c577c49c69c4ad3247b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989deeff85cb76f4040a257764a781b0a0", "guid": "bfdfe7dc352907fc980b868725387e98a831588d00c9d9fbe985c68db31a8316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e34386a42e964af0a2668a0e5c4c1fa4", "guid": "bfdfe7dc352907fc980b868725387e98ac31e4e624acc496455f545102abf27d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f90b2b8ac74ac2e9d770b2b6fd4c803", "guid": "bfdfe7dc352907fc980b868725387e98678477970f7aaf645bcb32c796d6bdb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821cb157cde78063b64356b56cedde815", "guid": "bfdfe7dc352907fc980b868725387e98e45f2228315750713d2a16b96b0803d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1392b6264a3712c58cf726f7668936", "guid": "bfdfe7dc352907fc980b868725387e985eac5da3bb5b17db6d12c12cb165ee7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98482c4b708f019c78c1bc9f2cd75d8ded", "guid": "bfdfe7dc352907fc980b868725387e98bf3b21caa6db438e24f87cb88e723322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba84e37c20f9f1a0712f6598720ca9a7", "guid": "bfdfe7dc352907fc980b868725387e98abcd258297f7961ea1fcf43827342ecf"}], "guid": "bfdfe7dc352907fc980b868725387e9834a83197f248be77b53c9d0158a2a40f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d6e5dc93de40640996855ac1715a298", "guid": "bfdfe7dc352907fc980b868725387e984562d35efba77befc7a5dc167a503b28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fa4b1f3dd06807aa9b18884274992f7", "guid": "bfdfe7dc352907fc980b868725387e981803a02bbe0ec05e9abab37f0fdd0be9"}], "guid": "bfdfe7dc352907fc980b868725387e98556ef35866536ee63b3a82282a79dcb2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cce23da8b85908c4453531f3793017d", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e980116d689091b80f026e477f6431e1d66", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}