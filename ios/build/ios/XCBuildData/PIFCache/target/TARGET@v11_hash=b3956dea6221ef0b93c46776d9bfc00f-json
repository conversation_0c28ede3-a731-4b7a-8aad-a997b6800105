{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a25a794ba208169226fdec2a08c6ff5d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9809fd6854c5b5036cd1490740d8f6f3ad", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894210195775b0fc97d28858f12c208eb", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98420b4162edddce351f10afcd17889acf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894210195775b0fc97d28858f12c208eb", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9875dc39196b1429710c627b19994b7eae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986dd703f8e95d4aa105f765acfb1662ff", "guid": "bfdfe7dc352907fc980b868725387e98363fc79fe749598774449a51b136f1f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fd66e85989275d8074232cf58e06545", "guid": "bfdfe7dc352907fc980b868725387e98f918a34afba7e1491c15a3c461db4ca0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd748b7847153f681bd32fbb16cf31bd", "guid": "bfdfe7dc352907fc980b868725387e9894d0d34a84cbfd39f0a8c7760d44cc20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c795ccb7e8d445d3642dc4e98930f931", "guid": "bfdfe7dc352907fc980b868725387e98dad96aa9ddd7b2e4f0dbee46251077b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da434139510d4a1179412f02ce87b1a7", "guid": "bfdfe7dc352907fc980b868725387e9835c3b279b0b5792d1b3d7ca1698c96b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7f1428c3e8ade254bc03e1bd52d2313", "guid": "bfdfe7dc352907fc980b868725387e98e8cd09579d85eff0e5049bbce610d031", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6a3e982e66be7f78294aaf0d7dd244a", "guid": "bfdfe7dc352907fc980b868725387e986cd199a0eefdf43296576771360dd06d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c97382ecf8b0583d8e9c809f0600aefc", "guid": "bfdfe7dc352907fc980b868725387e98986b3eede13540ca520f232070c7ddf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6ad5cf849054f3766b8170ffbc85e74", "guid": "bfdfe7dc352907fc980b868725387e983242ff2b0b26a9464e32c22337e61abd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6bf29563757c924ab57d9b6afd1cbe6", "guid": "bfdfe7dc352907fc980b868725387e985c5844b1adb770110d207996ccea2959", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fa3d9bf7f9e0e06ea71ea05293b5979", "guid": "bfdfe7dc352907fc980b868725387e98e73d9185b22f7cb123ee56671139bac6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981acad2bcee472c3b1bbfb56d93fca8d7", "guid": "bfdfe7dc352907fc980b868725387e98dc4f110ab0f7871352dab4eff861df6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5880c5e5719d61b5fae07a11560eb52", "guid": "bfdfe7dc352907fc980b868725387e981dd74c7072d296227d8bc45c9d75c5bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad68f5e3346de0dbf3f8b3d18baf705", "guid": "bfdfe7dc352907fc980b868725387e9808b24076d5e9294ad80aae0c7d63bb56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987205e79f0ecdaef1ab00c30dbe879a53", "guid": "bfdfe7dc352907fc980b868725387e98ff8e60117e98df887143c259b3e72aa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a86c09213107d22bf47524b0fb5eac7f", "guid": "bfdfe7dc352907fc980b868725387e98e2b1b95bbed12f62af60cc674ca31d09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9f4342d958eb5b98f28d1b31a72a4a6", "guid": "bfdfe7dc352907fc980b868725387e98b92debca892227a6d910a969882454d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8bae2e87e6ba8f5f528e3612b130d47", "guid": "bfdfe7dc352907fc980b868725387e98cba6f3ef94c3a7bc8a7f0dff2d211f81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980667de1a8e8bf002b0c799b9235e93d0", "guid": "bfdfe7dc352907fc980b868725387e98a49aeed739dff54aae77aa9858329573", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cce10d7fa67acaafc20c2baaae520a3a", "guid": "bfdfe7dc352907fc980b868725387e9807404fa4b4485416a3f64603118e3381", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb747815241d642033026301cbef63d", "guid": "bfdfe7dc352907fc980b868725387e984deb9c4b7452172b87060304863033f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b772b474eba9b6378065a06289bb4d", "guid": "bfdfe7dc352907fc980b868725387e98157d8c8d00dfaf71b70ab1a7dd4ecb44", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983031e1d6057db9dc5d37d5d1657afd1a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9884ca7dca06fa4d3123c1ef40b5430c02", "guid": "bfdfe7dc352907fc980b868725387e980549bc2ae36d2ad10cb86cd85891d11b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cff5db3f3152ed2324a90c88499840cf", "guid": "bfdfe7dc352907fc980b868725387e98d58c6218f34780e9b5a7d7634fd6d8b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a8288b2161261857e69ebc159b2ea37", "guid": "bfdfe7dc352907fc980b868725387e9820a374854663867e3634c17d827fb98a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf76afe4dd7ee9eb460276bc48c07f10", "guid": "bfdfe7dc352907fc980b868725387e980baf3563c84d85ab9c0087b5ce8b5ce8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980278c74c57b70552f8b2d2db1a8a5c0d", "guid": "bfdfe7dc352907fc980b868725387e98144d2d5db024ef75b14889620dfd8944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ba35f89f05595b539ee6eb09afab2e5", "guid": "bfdfe7dc352907fc980b868725387e98c6e2a1806bd7fc131212a99103fa82c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986789d8d29b09650dee4da3c8030214f5", "guid": "bfdfe7dc352907fc980b868725387e98448a45d96bc4bf687f7e3edc65cf8b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffa97c7711de6b0a5447fe516b521035", "guid": "bfdfe7dc352907fc980b868725387e98d0f86c8d91b8fb7bd75034a4a68500b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d34619ebac5b6c935322843a6658073", "guid": "bfdfe7dc352907fc980b868725387e989ce9ad8c8ca78257a34ed3681a4c3e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dfb4a320c067e8e409e832254b1b74c", "guid": "bfdfe7dc352907fc980b868725387e9896599d987390c5c5a59f39112cf1df0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a3a1e5085fdb57720b876336dae804e", "guid": "bfdfe7dc352907fc980b868725387e98b7565a027a4cf56511d4926335fc665c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843c5f809d27288813a1a6fe89fbb2e16", "guid": "bfdfe7dc352907fc980b868725387e9852d44123b7588084b0c8197be6e3cc57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882fb1d89a784cc1e2d471faaeb930db8", "guid": "bfdfe7dc352907fc980b868725387e981253368822840b6a0c24602c329e6f18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6899e6eb4ea2703224346859fca3c50", "guid": "bfdfe7dc352907fc980b868725387e9858411544fb1febc08e84de3fa55d12d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ff82bc3f1ec2726a829d8e63824ecd1", "guid": "bfdfe7dc352907fc980b868725387e98a0e0478f9964ff8abe8db112642c90c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888e0b4fe9dccb04267853dd0ec3b805c", "guid": "bfdfe7dc352907fc980b868725387e989707bf0dbb6722217b0a79fce6a78d7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825782fdd223b296be6c35690c805e4ae", "guid": "bfdfe7dc352907fc980b868725387e983d95f27e23a9c328f6168e13cc54a247"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7a5d3a8dfd85b89609246e8428f1636", "guid": "bfdfe7dc352907fc980b868725387e98d68d9024067e3d2d3d12ea91fab01b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c668b1f9f920cf3d643eafcab8136d9d", "guid": "bfdfe7dc352907fc980b868725387e9839025dbd7eaae095c4581d6e6f7cd041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a488b02c2dec6542d81000cf3fd3fe0", "guid": "bfdfe7dc352907fc980b868725387e988b4076d41e167e038153ca45ede13bbf"}], "guid": "bfdfe7dc352907fc980b868725387e9860109ab414f3ec9ba5ad7a385a0ab312", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d6e5dc93de40640996855ac1715a298", "guid": "bfdfe7dc352907fc980b868725387e981080364e6ec042836ce3bce30e32f7fd"}], "guid": "bfdfe7dc352907fc980b868725387e98019f36a6e77eeec9db7518a272dc20ae", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9815fbc70ba7e4fb17d5d9f0052630789e", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9884cad207e697257f6d96f7079928ffa0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}