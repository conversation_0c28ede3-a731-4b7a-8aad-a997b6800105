PODS:
  - Firebase/CoreOnly (10.22.0):
    - FirebaseCore (= 10.22.0)
  - firebase_core (2.27.0):
    - Firebase/CoreOnly (= 10.22.0)
    - Flutter
  - FirebaseCore (10.22.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)

DEPENDENCIES:
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - GoogleUtilities
    - PromisesObjC

EXTERNAL SOURCES:
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"

SPEC CHECKSUMS:
  Firebase: 797fd7297b7e1be954432743a0b3f90038e45a71
  firebase_core: 2a80983e98480c7f02ff001358621c8d7f1ff8f0
  FirebaseCore: 0326ec9b05fbed8f8716cddbf0e36894a13837f7
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47

PODFILE CHECKSUM: ba6210cd2244d54f18c3eae06976f3feae35cb3e

COCOAPODS: 1.16.2
